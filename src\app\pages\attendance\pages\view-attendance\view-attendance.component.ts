import { CommonModule } from '@angular/common';
import { ChangeDetectorRef, Component, EventEmitter, Input, OnChanges, Output, SimpleChanges } from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { BaseComponent } from 'src/app/shared/components/base-component/base.component';
import { SharedModule } from 'src/app/shared/shared.module';
import { takeUntil } from 'rxjs';
import { API_URL } from 'src/app/shared/constants/api-url.constants';
import { MatInputModule } from '@angular/material/input';
import { SchedulerService } from 'src/app/pages/scheduler/pages/scheduler-wrapper/pages/schedule/services';
import { AppToasterService } from 'src/app/shared/services';
import { CBGetResponse } from 'src/app/shared/models';
import { ClassTypes, ScheduleDetailsView, StudentDetail } from 'src/app/pages/scheduler/pages/scheduler-wrapper/pages/schedule/models';
import { CommonUtils } from 'src/app/shared/utils';
import { MatIconModule } from '@angular/material/icon';
import { AttendanceService } from '../../services';
import { MatSidenavModule } from '@angular/material/sidenav';
import { ViewStudentComponent } from '../../../members/pages/students/pages/view-student/view-student.component';
import { DependentInformations, Students } from 'src/app/pages/members/pages/students/models';
import { DependentService } from 'src/app/pages/profile/services';
import { Account } from 'src/app/auth/models/user.model';
import { MatTooltipModule } from '@angular/material/tooltip';
import { AssignedInstructors } from 'src/app/pages/schedule-classes/pages/ensemble-class/models';
import { LocalDatePipe } from 'src/app/shared/pipe';
import { InstructorDetails, Instructors } from 'src/app/pages/members/pages/instructors/models';
import { InstructorService } from 'src/app/schedule-introductory-lesson/services';
import { ViewInstructorComponent } from 'src/app/pages/members/pages/instructors/pages/view-instructor/view-instructor.component';

const DEPENDENCIES = {
  MODULES: [MatButtonModule, CommonModule, SharedModule, MatInputModule, MatIconModule, MatSidenavModule, MatTooltipModule],
  COMPONENTS: [ViewStudentComponent, ViewInstructorComponent],
  PIPES: [LocalDatePipe]
};

@Component({
  selector: 'app-view-attendance',
  standalone: true,
  imports: [...DEPENDENCIES.MODULES, ...DEPENDENCIES.COMPONENTS, ...DEPENDENCIES.PIPES],
  templateUrl: './view-attendance.component.html',
  styleUrl: './view-attendance.component.scss'
})
export class ViewAttendanceComponent extends BaseComponent implements OnChanges {
  @Input() selectedScheduleId!: number | null;
  @Input() currentUser$!: Account | null;

  selectedScheduleDetail!: ScheduleDetailsView | null;
  selectedStudentInfo!: DependentInformations | undefined;
  selectedInstructorInfo!: InstructorDetails | null;
  classTypes = ClassTypes;
  isViewStudentSideNavOpen = false;
  isViewInstructorSideNavOpen = false;
  isAttendanceUpdated = false;

  @Output() closeSideNav = new EventEmitter<void>();
  @Output() updateAttendance = new EventEmitter<boolean>();

  constructor(
    protected readonly schedulerService: SchedulerService,
    private readonly attendanceService: AttendanceService,
    private readonly toasterService: AppToasterService,
    private readonly dependentService: DependentService,
    private readonly cdr: ChangeDetectorRef,
    private readonly instructorService: InstructorService
  ) {
    super();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['selectedScheduleId']?.currentValue) {
      this.selectedScheduleId = changes['selectedScheduleId']?.currentValue;
      this.getScheduleDetails(this.selectedScheduleId);
    }
  }

  getScheduleDetails(id: number | null): void {
    this.showPageLoader = true;
    this.schedulerService
      .get<CBGetResponse<ScheduleDetailsView>>(`${API_URL.scheduleLessonDetails.getScheduleLessonDetailForView}?id=${id}`)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: CBGetResponse<ScheduleDetailsView>) => {
          this.selectedScheduleDetail = res.result;
          this.showPageLoader = false;
          this.cdr.detectChanges();
        },
        error: () => {
          this.showPageLoader = false;
          this.cdr.detectChanges();
        }
      });
  }

  getScheduleDetailDescription(selectedScheduleDetail: ScheduleDetailsView | null): string {
    switch (selectedScheduleDetail?.classType) {
      case this.classTypes.GROUP_CLASS:
        return selectedScheduleDetail?.groupClassName;

      case this.classTypes.SUMMER_CAMP:
        return selectedScheduleDetail?.campName;

      case this.classTypes.ENSEMBLE_CLASS:
        return selectedScheduleDetail?.ensembleClassName;

      case this.classTypes.MAKE_UP:
        return `${selectedScheduleDetail?.instrumentName} Make-Up Lesson`;

      default:
        return `${selectedScheduleDetail?.instrumentName} Lesson`;
    }
  }

  isFutureEvent(scheduleDate?: string): boolean {
    const currentDate = new Date();
    const formattedDate = new Date(scheduleDate!);
    return formattedDate > currentDate;
  }

  markAttendance(isPresent: boolean, studentDetail: StudentDetail): void {
    if (this.isFutureEvent(this.selectedScheduleDetail?.scheduleDate)) {
      this.toasterService.error(this.constants.errorMessages.invalidAttendance.replace('{item}', 'future dates'));
      return;
    }
    this.attendanceService
      .add(
        {
          studentId: studentDetail.studentId,
          scheduleLessonDetailId: this.selectedScheduleDetail?.id,
          classType: this.selectedScheduleDetail?.classType,
          isPresent: isPresent
        },
        API_URL.crud.createOrUpdate
      )
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: () => {
          if (this.selectedScheduleDetail?.studentDetails.some(student => student.isPresent === null)) {
            this.isAttendanceUpdated = true;
          }
          this.getScheduleDetails(this.selectedScheduleDetail?.id!);
          this.toasterService.success(this.constants.successMessages.markedSuccessfully.replace('{item}', 'Attendance'));
          this.showBtnLoader = false;
          this.cdr.detectChanges();
        },
        error: () => {
          this.showBtnLoader = false;
          this.cdr.detectChanges();
        }
      });
  }

  getDependentDetails(studentId: number | null): void {
    this.dependentService
      .getList<CBGetResponse<Students>>(`${API_URL.dependentInformations.getDependentInformationForView}?id=${studentId}`)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: CBGetResponse<Students>) => {
          this.selectedStudentInfo = res.result.dependentInformation;
          this.cdr.detectChanges();
        }
      });
  }

  getInstructorDetails(instructorId: number | null): void {
    this.instructorService
      .getList<CBGetResponse<Instructors>>(`${API_URL.instructorDetails.getInstructorDetailForView}?id=${instructorId}`)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: CBGetResponse<Instructors>) => {
          this.selectedInstructorInfo = res.result.instructorDetail;
          this.cdr.detectChanges();
        }
      });
  }

  getInstructorNames(array: AssignedInstructors[]) {
    return array
      .slice(1)
      .map(item => item.name)
      .join(', ');
  }

  toggleStudentDetail(isOpen: boolean, studentId: number | null): void {
    this.isViewStudentSideNavOpen = isOpen;
    if (isOpen) {
      this.getDependentDetails(studentId);
    }
  }

  toggleInstructorDetail(isOpen: boolean, instructorId: number | null): void {
    this.isViewInstructorSideNavOpen = isOpen;
    if (isOpen) {
      this.getInstructorDetails(instructorId);
    }
  }

  getInitials(name: string): string {
    return CommonUtils.getInitialsUsingFullName(name);
  }

  getTimeDiff(start: string, end: string): number {
    return CommonUtils.calculateTimeDifference(new Date(start), new Date(end));
  }

  onCloseModal(): void {
    if (this.isAttendanceUpdated) {
      this.updateAttendance.emit(true);
    }
    this.isAttendanceUpdated = false;
    this.closeSideNav.emit();
  }
}
