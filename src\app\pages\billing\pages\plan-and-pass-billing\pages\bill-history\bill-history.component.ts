import { ChangeDetectorRef, Component, EventEmitter, Input, OnChanges, SimpleChanges } from '@angular/core';
import { BaseComponent } from 'src/app/shared/components/base-component/base.component';
import { CommonModule } from '@angular/common';
import { DirectivesModule } from 'src/app/shared/directives/directives.module';
import { CBResponse, IdNameModel } from 'src/app/shared/models';
import { API_URL } from 'src/app/shared/constants/api-url.constants';
import { takeUntil } from 'rxjs';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { NgxPaginationModule } from 'ngx-pagination';
import { CommonUtils } from 'src/app/shared/utils';
import { ClassTypes } from 'src/app/pages/scheduler/pages/scheduler-wrapper/pages/schedule/models';
import { SchedulerService } from 'src/app/pages/scheduler/pages/scheduler-wrapper/pages/schedule/services';
import { PlanSummaryService } from 'src/app/pages/settings/pages/plan/services';
import { PaymentService } from 'src/app/pages/schedule-classes/services';
import {
  PlanAndPassBillingFilters,
  BillStatus,
  BillHistoryRes,
  UserBillingTransactionsResponse,
  BillingDetails,
  DependentBillingDetail
} from '../../../../models';
import { SharedModule } from 'src/app/shared/shared.module';
import { MatSelectModule } from '@angular/material/select';
import { FormsModule } from '@angular/forms';
import { All } from 'src/app/pages/settings/pages/plan/models';
import { AuthService } from 'src/app/auth/services';
import { ActivatedRoute, Router } from '@angular/router';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { provideNativeDateAdapter } from '@angular/material/core';
import moment from 'moment';
import { MatIconModule } from '@angular/material/icon';
import { FilterPipe, LocalDatePipe } from 'src/app/shared/pipe';
import { Account } from 'src/app/auth/models/user.model';
import { InfiniteScrollDirective } from 'ngx-infinite-scroll';
import { MatSidenavModule } from '@angular/material/sidenav';
import { BillingSidenavComponent } from '../billing-sidenav/billing-sidenav.component';

const DEPENDENCIES = {
  MODULES: [
    CommonModule,
    DirectivesModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    NgxPaginationModule,
    SharedModule,
    MatSelectModule,
    FormsModule,
    MatTooltipModule,
    MatDatepickerModule,
    MatIconModule,
    MatSidenavModule
  ],
  PIPES: [FilterPipe],
  COMPONENTS: [BillingSidenavComponent]
};

@Component({
  selector: 'app-bill-history',
  standalone: true,
  imports: [...DEPENDENCIES.MODULES, ...DEPENDENCIES.PIPES, ...DEPENDENCIES.COMPONENTS],
  providers: [provideNativeDateAdapter()],
  templateUrl: './bill-history.component.html',
  styleUrl: './bill-history.component.scss'
})
export class BillHistoryComponent extends BaseComponent implements OnChanges {
  @Input() currentUser$!: Account | null;
  @Input() studentList!: Array<IdNameModel>;

  selectedDependentId!: number | undefined;
  selectedUserId!: number | undefined;
  selectedBillingId!: number | undefined;
  billHistoryDetails: Array<UserBillingTransactionsResponse> = [];
  totalCount!: number;
  searchTerm = '';
  classType = ClassTypes;
  billStatus = BillStatus;
  selectedDetailedBillingData!: BillingDetails | null;
  isDetailedBillingSideNavOpen = false;
  all = All;
  isInitialLoad = true;

  filters: PlanAndPassBillingFilters = {
    statusFilter: 0,
    startDateFilter: moment().subtract(1, 'month').format(this.constants.dateFormats.yyyy_MM_DD),
    endDateFilter: moment().format(this.constants.dateFormats.yyyy_MM_DD)
  };

  constructor(
    private readonly cdr: ChangeDetectorRef,
    protected readonly planSummaryService: PlanSummaryService,
    protected readonly schedulerService: SchedulerService,
    private readonly paymentService: PaymentService,
    private readonly activatedRoute: ActivatedRoute,
    private readonly router: Router
  ) {
    super();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['currentUser$']?.currentValue) {
      this.currentUser = changes['currentUser$'].currentValue;
      this.getCurrentId();
    }
  }

  getCurrentId(): void {
    this.activatedRoute.queryParams.subscribe((params: any) => {
      if (params.activeTab === 'Bill History') {
        let shouldCallAPI = this.isInitialLoad;

        const newDependentId = params.dependentId ? +params.dependentId : 0;
        const newUserId = params.userId ? +params.userId : 0;

        if (this.selectedDependentId !== newDependentId || this.selectedUserId !== newUserId) {
          this.selectedDependentId = newDependentId;
          this.selectedUserId = newUserId;
          shouldCallAPI = true;
        }

        if (params.id) {
          this.selectedBillingId = +params.id;
        } else {
          this.selectedBillingId = 0;
        }

        if (shouldCallAPI) {
          this.getBillHistoryDetails();
          this.isInitialLoad = false;
        }
      }
    });
  }

  openStudentDetailPage(dependentId: number): void {
    if (this.currentUser?.userRoleId === this.constants.roleIds.CLIENT) {
      return;
    }
    this.router.navigate([this.path.members.root, this.path.members.clients], {
      queryParams: { dependentId: dependentId }
    });
  }

  setStudentDetail(student: IdNameModel | null): void {
    this.selectedDependentId = student?.id ?? 0;
    this.selectedUserId = student?.accountManagerId ?? 0;
    this.getBillHistoryDetails();
  }

  getFilterParams() {
    return CommonUtils.cleanObjectByRemovingKeysWithoutValue({
      UserId: this.currentUser?.userRoleId === this.constants.roleIds.CLIENT ? this.currentUser?.userId : this.selectedUserId,
      dependentInformationId: this.selectedDependentId,
      CreatedStartDate: moment(this.filters.startDateFilter).format(this.constants.dateFormats.yyyy_MM_DD),
      CreatedEndDate: moment(this.filters.endDateFilter).format(this.constants.dateFormats.yyyy_MM_DD)
    });
  }

  getBillHistoryDetails(): void {
    if (!this.filters.endDateFilter) {
      return;
    }
    this.showPageLoader = true;
    this.paymentService
      .getListWithFilters<CBResponse<UserBillingTransactionsResponse>>(
        this.getFilterParams(),
        `${API_URL.payment.getAllTransactionsOfUser}`
      )
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: CBResponse<UserBillingTransactionsResponse>) => {
          this.billHistoryDetails = res.result.items;
          this.totalCount = res.result.totalCount;
          if (this.selectedBillingId) {
            this.isDetailedBillingSideNavOpen = true;
            this.selectedDetailedBillingData =
              this.billHistoryDetails.find(detail => detail.userBillingTransactions.id === this.selectedBillingId)
                ?.userBillingTransactions ?? null;
          }
          this.showPageLoader = false;
          this.cdr.detectChanges();
        },
        error: () => {
          this.showPageLoader = false;
          this.cdr.detectChanges();
        }
      });
  }

  getCurrentPlanDetail(billingDetail: BillingDetails): DependentBillingDetail | undefined {
    return billingDetail.dependentBillingDetails.find(detail => detail.planId === billingDetail.planId);
  }

  toggleDetailedBillingSideNav(isOpen: boolean, billDetail: BillingDetails | null): void {
    this.isDetailedBillingSideNavOpen = isOpen;
    this.selectedDetailedBillingData = billDetail;

    if (isOpen && billDetail) {
      this.router.navigate([this.path.billing.root, this.path.billing.planAndPass], {
        queryParams: { activeTab: 'Bill History', id: billDetail.id },
        queryParamsHandling: 'merge'
      });
    } else {
      this.router.navigate([this.path.billing.root, this.path.billing.planAndPass], {
        queryParams: { activeTab: 'Bill History', id: null },
        queryParamsHandling: 'merge'
      });
    }
  }
}
