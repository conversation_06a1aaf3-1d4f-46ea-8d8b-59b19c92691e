<div class="o-sidebar-wrapper">
    <div class="o-sidebar-header">
        <div class="title">Billing Details</div>
        <div class="action-btn-wrapper">
            <button mat-raised-button color="accent" class="mat-accent-btn back-btn" type="button"
                (click)="closeSideNavFun()">Close</button>
        </div>
    </div>
    <div class="divider"></div>
    <div class="o-sidebar-body">
        @if (billingDetails) {
                <div class="plan-details-section">
                    <div class="plan-details-card">
                        <div class="section-header">
                            <i class="material-icons">person</i>
                            <span>Client Details</span>
                        </div>
                        <div class="plan-detail-row">
                            <div class="plan-detail-label">Account Manager</div>
                            <div class="plan-detail-value">
                                {{ billingDetails.accountManagerName | titlecase }}
                            </div>
                        </div>

                        <div class="plan-detail-row">
                            <div class="plan-detail-label">Dependent Name</div>
                            @if (isOpenBill) {
                                <div class="plan-detail-value">
                                    {{ dependentDetails[0] }}
                                    @if (dependentDetails.length > 1) {
                                        <div class="dot"></div>
                                        <span [matTooltip]="dependentDetails.join(', ')"> +{{ dependentDetails.length - 1 }}</span>
                                    }
                                </div>
                            }
                            @else {
                                <div class="plan-detail-value pointer" (click)="openStudentDetailPage(billingDetails.dependentInformationId)">{{ billingDetails.dependentName | titlecase }}</div>
                            }
                        </div>

                        <div class="plan-detail-row">
                            <div class="plan-detail-label">Phone No.</div>
                            <div class="plan-detail-value">{{ billingDetails.accountManagerPhone }}</div>
                        </div>

                        <div class="plan-detail-row">
                            <div class="plan-detail-label">Email address</div>
                            <div class="plan-detail-value">{{ billingDetails.accountManagerEmail }}</div>
                        </div>
                    </div>
                </div>

                <div class="plan-details-section" *ngIf="!isOpenBill">
                    <div class="plan-details-card">
                        <div class="section-header">
                            <i class="material-icons">description</i>
                            <span>Plan Details</span>
                        </div>
                        <div class="plan-detail-row">
                            <div class="plan-detail-label">Schedule Name</div>
                            <div class="plan-detail-value">
                                @switch(billingDetails.classType) {
                                    @case (classTypes.INTRODUCTORY) {
                                        Introductory {{ billingDetails.instrumentName }} Lesson
                                    }
                                    @case (classTypes.RECURRING) {
                                        @if (currentPlanDetail) {
                                            Weekly {{ currentPlanDetail.planInstrument }} Lessons ({{ planSummaryService.getPlanType(currentPlanDetail.planType) }} - {{ planSummaryService.getPlanSummary(currentPlanDetail.planDetails) }})
                                        }
                                    }
                                    @case (classTypes.GROUP_CLASS) {
                                        {{ billingDetails.groupClassName }}
                                    }
                                    @case (classTypes.SUMMER_CAMP) {
                                        {{ billingDetails.summerCampName }}
                                    }
                                    @case (classTypes.ENSEMBLE_CLASS) {
                                        {{ billingDetails.ensembleClassName }}
                                    }
                                }
                            </div>
                        </div>

                        <div class="plan-detail-row">
                            <div class="plan-detail-label">Transaction Id</div>
                            <div class="plan-detail-value">#{{ billingDetails.transactionId }}</div>
                        </div>

                        <div class="plan-detail-row">
                            <div class="plan-detail-label">Payment Date</div>
                            <div class="plan-detail-value">{{ billingDetails.billPaymentDate | date: 'mediumDate' }}</div>
                        </div>

                        <div class="plan-detail-row">
                            <div class="plan-detail-label">Transaction Type</div>
                            <div class="plan-detail-value">{{ transactionTypes[billingDetails.transactionType] | titlecase }}</div>
                        </div>

                        <div class="plan-detail-row" *ngIf="billingDetails.chequeNumber">
                            <div class="plan-detail-label">Cheque Number</div>
                            <div class="plan-detail-value">{{ billingDetails.chequeNumber }}</div>
                        </div>
                    </div>
                </div>

            @if (otherPlanDetails.length > 0) {
                <div class="existing-plans-section">
                    <div class="existing-plans-list">
                        <div class="section-header">
                            <i class="material-icons">list</i>
                            <span>Existing Plan Details</span>
                        </div>
                        <div class="existing-plan-wrapper">
                            @for (plan of otherPlanDetails; track plan.dependentInformationId) {
                                <div class="existing-plan-item">
                                    <div class="plan-number">{{ $index + 1 }}.</div>
                                    <div class="plan-info">
                                        <div class="plan-name">
                                            Weekly {{ plan?.planInstrument }} Lessons ({{ planSummaryService.getPlanType(plan.planType) }} - {{ planSummaryService.getPlanSummary(plan.planDetails) }})
                                        </div>
                                        <div class="plan-meta">
                                            <span>For {{ plan.dependentName }}</span>
                                            <span class="dot"></span>
                                            <span>Plan End Date: {{ (plan.planEndDate || plan.scheduleEndDate) | date:'MMM dd, yyyy' }}</span>
                                        </div>
                                    </div>
                                    <div class="plan-amount">${{ plan.planAmount | number:'1.2-2' }}/Month</div>
                                </div>
                                <div class="dotted-divider" *ngIf="!$last"></div>
                            }
                        </div>
                    </div>
                </div>
            }

            <div class="plan-details-section" *ngIf="!isOpenBill">
                <div class="plan-details-card">
                    <div class="section-header">
                        <i class="material-icons">payment</i>
                        <span>Payment Details</span>
                    </div>
                        <div class="plan-detail-row">
                            <span class="plan-detail-label">Total Amount</span>
                            <span class="plan-detail-value">${{ billingDetails.totalAmount | number:'1.2-2' }}</span>
                        </div>

                        <div class="plan-detail-row">
                            <span class="plan-detail-label">Discount Applied</span>
                            <span class="plan-detail-value">-${{ billingDetails.discountedAmount | number:'1.2-2' }}</span>
                        </div>

                        <div class="dotted-divider"></div>

                        <div class="plan-detail-row">
                            <span class="plan-detail-label">Paid Amount</span>
                            <span class="plan-detail-value primary-color">${{ billingDetails.paidAmount | number:'1.2-2' }}</span>
                        </div>
                </div>
            </div>

            <div class="plan-details-section" *ngIf="isOpenBill">
                <div class="plan-details-card">
                    <div class="section-header">
                        <i class="material-icons">payment</i>
                        <span>Payment Details</span>
                    </div>
                    @for (dependentDetails of billingDetails.dependentBillingDetails; track $index) {
                        <div class="plan-detail-row">
                            <span class="plan-detail-label">
                                Weekly {{ dependentDetails?.planInstrument }} Lessons ({{ planSummaryService.getPlanType(dependentDetails.planType) }} - {{ planSummaryService.getPlanSummary(dependentDetails.planDetails) }})
                            </span>
                            <span class="plan-detail-value">{{ dependentDetails.planAmount | currency: 'USD' : 'symbol' : '1.2-2' }}</span>
                        </div>
                    }

                    <div class="plan-detail-row">
                        <span class="plan-detail-label">
                            Discount Applied
                        </span>
                        <span class="plan-detail-value">-{{ getTotalDiscount | currency: 'USD' : 'symbol' : '1.2-2' }}</span>
                    </div>

                    <div class="dotted-divider"></div>

                    <div class="plan-detail-row">
                        <span class="plan-detail-label">
                            Total Amount
                        </span>
                        <span class="plan-detail-value primary-color">{{ billingDetails.paidAmount | currency: 'USD' : 'symbol' : '1.2-2' }}</span>
                    </div>
                </div>
            </div>
        }
    </div>
</div>