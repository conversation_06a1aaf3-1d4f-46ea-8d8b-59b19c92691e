import { CommonModule } from '@angular/common';
import { ChangeDetectorRef, Component, OnInit } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatSelectModule } from '@angular/material/select';
import { MatTooltipModule } from '@angular/material/tooltip';
import { ActivatedRoute, Router } from '@angular/router';
import { takeUntil } from 'rxjs';
import { BaseComponent } from 'src/app/shared/components/base-component/base.component';
import { SharedModule } from 'src/app/shared/shared.module';
import { DashboardData, UpcomingBirthday, InstructorWorkingHours } from './models';
import { DashboardService } from './services';
import { AuthService } from 'src/app/auth/services';
import { CommonUtils } from 'src/app/shared/utils';
import { API_URL } from 'src/app/shared/constants/api-url.constants';
import { CBGetResponse, CBResponse } from 'src/app/shared/models';
import { PlanStatus } from '../plans-and-passes/models';
import { MaintenanceRequestsState } from '../requests/pages/maintenance-request/models';
import { ClassTypes, ScheduleDetailsView, StudentPlans } from '../scheduler/pages/scheduler-wrapper/pages/schedule/models';
import { EChartsOption } from 'echarts';
import { LeaveStatus } from '../requests/pages/leave-request/models';
import { ClientDashboardComponent } from './pages/client-dashboard/client-dashboard.component';
import { ROLE_IDS } from 'src/app/shared/constants';
import { PaymentService } from '../schedule-classes/services';
import { BillStatus, UserBillingTransactionsResponse } from '../billing/models';
import { StudentPlanService } from '../scheduler/pages/scheduler-wrapper/pages/schedule/services';
import { LocalDatePipe } from 'src/app/shared/pipe';
import { NavigationService } from 'src/app/shared/services';
import { LocalStorageService, StorageItem } from 'src/app/shared/services/local-storage.service';
import { Account } from 'src/app/auth/models/user.model';

const DEPENDENCIES = {
  MODULES: [CommonModule, SharedModule, MatButtonModule, MatIconModule, MatSelectModule, MatTooltipModule, FormsModule, MatTooltipModule],
  COMPONENTS: [ClientDashboardComponent],
  PIPES: [LocalDatePipe]
};

@Component({
  selector: 'app-dashboard',
  standalone: true,
  imports: [...DEPENDENCIES.MODULES, ...DEPENDENCIES.COMPONENTS, ...DEPENDENCIES.PIPES],
  templateUrl: './dashboard.component.html',
  styleUrls: ['./dashboard.component.scss']
})
export class DashboardComponent extends BaseComponent implements OnInit {
  today = new Date();
  dashboardData!: DashboardData;
  todayBirthdays!: Array<UpcomingBirthday>;
  upcomingBirthdays!: Array<UpcomingBirthday>;
  scheduleLessonDetails!: Array<ScheduleDetailsView> | undefined;
  pendingPayments: Array<UserBillingTransactionsResponse> = [];
  expiringPlans: Array<StudentPlans> = [];
  chartOptions!: EChartsOption;
  availableLeaves!: number;
  usedLeaves!: number;
  totalLeaves!: number;
  totalWorkingHours!: number;
  completedWorkingHours!: number;
  pendingWorkingHours!: number;
  workingHoursChartOptions!: EChartsOption;
  showSupervisorWorkingHours = false;
  planCancelStatus = PlanStatus;
  maintenanceRequestStatus = MaintenanceRequestsState;
  classTypes = ClassTypes;
  roleIds = ROLE_IDS;
  isOnLeaveCardCollapsed = false;
  showAllOnLeave = false;
  showSupervisorSchedule = false;
  selectedDependentId!: number;
  selectedDependentName!: string;
  showTodayWorkingHours = true;
  workingHoursOptions = [
    { label: "Today's Working Hours", value: true },
    { label: `${new Date().toLocaleString('default', { month: 'long' })}\'s Working Hours`, value: false }
  ];

  constructor(
    private readonly dashboardService: DashboardService,
    private readonly studentPlanService: StudentPlanService,
    private readonly paymentService: PaymentService,
    private readonly authService: AuthService,
    private readonly cdr: ChangeDetectorRef,
    private readonly router: Router,
    private readonly activatedRoute: ActivatedRoute,
    private readonly navigationService: NavigationService,
    private readonly localStorageService: LocalStorageService
  ) {
    super();
  }

  ngOnInit(): void {
    this.getCurrentUser();
    this.getCurrentId();
  }

  getCurrentUser(): void {
    this.currentUser = this.localStorageService.getItem(StorageItem.CurrentUser) as Account;
    if (
      this.currentUser?.userRoleId === this.constants.roleIds.ADMIN ||
      this.currentUser?.userRoleId === this.constants.roleIds.DESK_MANAGER
    ) {
      this.getPendingPayments();
      this.getExpiringPlans();
    }
  }

  getCurrentId(): void {
    this.activatedRoute.queryParams.subscribe((params: any) => {
      if (params.dependentId) {
        this.selectedDependentId = +params.dependentId;
      } else {
        this.selectedDependentId = 0;
      }
      this.getSelectedDependentName(this.selectedDependentId);
      this.getDashboardData(this.selectedDependentId);
    });
  }

  getSelectedDependentName(dependentId: number): void {
    if (!dependentId) {
      this.selectedDependentName = this.currentUser?.firstName + ' ' + this.currentUser?.lastName;
      return;
    }
    if (dependentId === this.currentUser?.dependentId) {
      this.selectedDependentName = this.currentUser?.firstName + ' ' + this.currentUser?.lastName;
      return;
    }
    this.selectedDependentName =
      this.currentUser?.dependentDetails?.find(dependent => dependent.id === dependentId)?.firstName +
        ' ' +
        this.currentUser?.dependentDetails?.find(dependent => dependent.id === dependentId)?.lastName || '';
  }

  getDashboardData(dependentId: number): void {
    this.showPageLoader = true;
    const filter = dependentId ? `?DependentIdFilter=${dependentId}` : '';
    this.dashboardService
      .getList<CBGetResponse<DashboardData>>(`${API_URL.octopusDashBoard.getDashboardDetails}${filter}`)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: res => {
          this.dashboardData = res.result;
          this.setBirthdayData();
          this.setScheduleData();
          this.setWorkingHoursData();
          this.setLeavesData();
          this.showPageLoader = false;
          this.cdr.detectChanges();
        },
        error: () => {
          this.showPageLoader = false;
          this.cdr.detectChanges();
        }
      });
  }

  get currentInstructorWorkingHours(): Array<InstructorWorkingHours> {
    return this.showTodayWorkingHours
      ? this.dashboardData?.instructorDailyWorkingHours || []
      : this.dashboardData?.instructorMonthlyWorkingHours || [];
  }

  getFilterParams() {
    return CommonUtils.cleanObjectByRemovingKeysWithoutValue({
      Page: 1,
      PageSize: 2,
      RecurringBillStatus: BillStatus.OPEN
    });
  }

  getPendingPayments(): void {
    this.paymentService
      .getListWithFilters<CBResponse<UserBillingTransactionsResponse>>(this.getFilterParams(), API_URL.payment.getAllBillingDetailsOfUser)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: CBResponse<UserBillingTransactionsResponse>) => {
          this.pendingPayments = res.result.items;
          this.cdr.detectChanges();
        },
        error: () => {
          this.cdr.detectChanges();
        }
      });
  }

  getExpiringPlans(): void {
    this.studentPlanService
      .getList<CBResponse<StudentPlans>>(API_URL.studentPlans.getAllStudentPlansExpiringSoon)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: CBResponse<StudentPlans>) => {
          this.expiringPlans = res.result.items.filter(plan => plan.updatedAssignedPlanStatus === null);
          this.cdr.detectChanges();
        },
        error: () => {
          this.cdr.detectChanges();
        }
      });
  }

  setBirthdayData(): void {
    if (!this.dashboardData.upcomingBirthdays) {
      return;
    }
    this.upcomingBirthdays = this.dashboardData.upcomingBirthdays.filter(birthday => birthday.isUpcomingBirthday);
    this.todayBirthdays = this.dashboardData.upcomingBirthdays.filter(birthday => !birthday.isUpcomingBirthday);
  }

  setLeavesData(): void {
    if (!this.dashboardData.leaveBalance) {
      return;
    }
    if (this.dashboardData.leaveBalance.length) {
      this.totalLeaves = this.dashboardData.leaveBalance[0].totalLeaveDays;
      this.usedLeaves = this.dashboardData.leaveBalance[0].usedLeaveDays;
      this.availableLeaves = this.dashboardData.leaveBalance[0].remainingLeaveDays;
      this.chartOptions = CommonUtils.getChartOptions(this.availableLeaves, this.usedLeaves, this.totalLeaves);
    }
  }

  setScheduleData(selectedOption = false): void {
    this.showSupervisorSchedule = selectedOption;
    if (this.dashboardData.scheduleLessonDetails) {
      this.scheduleLessonDetails = this.dashboardData.scheduleLessonDetails;
    } else {
      const scheduleData = selectedOption
        ? this.dashboardData.instructorScheduleLessonDetails
        : this.dashboardData.supervisorScheduleLessonDetails;
      this.scheduleLessonDetails = scheduleData;
    }
  }

  setWorkingHoursSupervisorData(selectedOption = false): void {
    this.showSupervisorWorkingHours = selectedOption;
    this.cdr.detectChanges();
  }

  setWorkingHoursData(): void {
    const workingHoursData = this.showTodayWorkingHours ? this.dashboardData.dailyWorkingHours : this.dashboardData.mounthlyWorkingHours;

    if (workingHoursData) {
      this.totalWorkingHours = workingHoursData.totalWorkingHours;
      this.completedWorkingHours = workingHoursData.totalActualHours;
      this.pendingWorkingHours = this.totalWorkingHours - this.completedWorkingHours;
      this.workingHoursChartOptions = CommonUtils.getWorkingHoursChartOptions(
        this.completedWorkingHours,
        this.pendingWorkingHours,
        this.totalWorkingHours
      );
    }
  }

  onWorkingHoursOptionChange(): void {
    this.setWorkingHoursData();
    this.cdr.detectChanges();
  }

  openInstructorDetails(instructorId: number): void {
    this.navigationService.navigateToInstructorDetail(instructorId);
  }

  openStudentDetails(studentId: number): void {
    this.navigationService.navigateToStudentDetail(studentId);
  }

  formatHoursToHM(decimalHours: number): string {
    return CommonUtils.formatHoursToHM(decimalHours);
  }

  getInitialsUsingFullName(name: string): string {
    return CommonUtils.getInitialsUsingFullName(name);
  }

  getInitials(firstName: string, lastName: string): string {
    return CommonUtils.getInitials(firstName, lastName);
  }

  getTimeDiff(start: string, end: string): number | null {
    return CommonUtils.calculateTimeDifference(new Date(start), new Date(end));
  }

  getTimeBasedGreeting(): string {
    const hour = new Date().getHours();
    if (hour < 12) return 'Good Morning';
    if (hour < 18) return 'Good Afternoon';
    return 'Good Evening';
  }

  getStatusClass(status: number | undefined): string {
    switch (status) {
      case 1:
        return 'achPending';
      case 2:
        return 'enrolled';
      case 3:
        return 'notEnrolled';
      default:
        return '';
    }
  }

  navigateTo(path: string, queryParams = false): void {
    this.router.navigate([path], queryParams ? { queryParams: { activeTab: 'My Leave Request', mode: 'add' } } : {});
  }

  navigateToLeaveRequest(): void {
    if (this.currentUser?.userRoleId === this.constants.roleIds.ADMIN) {
      this.router.navigate(['/request/leave-request'], {
        queryParams: { activeTab: 'My Leave Request', status: LeaveStatus.PENDING_ACTION }
      });
    } else {
      this.router.navigate(['/request/leave-request'], {
        queryParams: { activeTab: 'Other Leave Request', status: LeaveStatus.PENDING_ACTION }
      });
    }
  }

  navigateToStudentViewPage(studentId: number): void {
    this.router.navigate([this.path.members.root, this.path.members.clients], { queryParams: { dependentId: studentId } });
  }

  shouldShowDivider(totalItemsLength: number, index: number, showAll: boolean, minShow: number): boolean {
    if (index === totalItemsLength - 1) {
      return false;
    }

    if (!showAll) {
      return index < minShow - 1;
    }

    return true;
  }
}
