<mat-sidenav-container class="example-container" [hasBackdrop]="true">
  <mat-sidenav
    [opened]="isPlanAssignedSideNavOpen"
    mode="over"
    position="end"
    fixedInViewport="true"
    class="sidebar-w-750"
    [disableClose]="true">
    @if (isPlanAssignedSideNavOpen) {
      <app-plan-assigned-success
        [selectedStudentDetails]="selectedStudentDetails"
        [selectedStudentPlan]="selectedStudentPlan"
        [selectedInstrumentName]="selectedInstrumentName"
        [totalAmount]="calculateTotalPrice(false)"
        [shoppingCart]="shoppingCart"
        [bookPlanFormValue]="bookPlanFormValue"
        [isPlanRenewal]="isPlanRenewal"
        (closeSideNav)="closeAll()"></app-plan-assigned-success>
    }
  </mat-sidenav>
</mat-sidenav-container>

<div class="o-sidebar-wrapper">
  <div class="o-sidebar-header">
    <div class="back-btn-wrapper">
      <img [src]="constants.staticImages.icons.arrowLeft" class="pointer" alt="" (click)="closeBookAssignedPlan()" />
      <div class="ps-2">
        <div class="title">{{ isPlanRenewal ? 'Re-new Plan' : 'Continue to Checkout' }}</div>
        <div class="name">
          <img [src]="constants.staticImages.icons.profileIcon" class="pe-1" alt="" />
          {{ selectedStudentDetails?.firstName | titlecase }}
          {{ selectedStudentDetails?.lastName | titlecase }}
        </div>
      </div>
    </div>
    <div class="action-btn-wrapper">
      <button
        mat-raised-button
        color="accent"
        class="mat-accent-btn back-btn"
        type="button"
        (click)="closeBookAssignedPlan()">
        Close
      </button>
      <button
        *ngIf="selectedStudentPlan"
        mat-raised-button
        color="primary"
        class="mat-primary-btn"
        type="button"
        (click)="isPlanRenewal ? onRenewPlan() : onSubmit()"
        [appLoader]="showBtnLoader">
        Submit
      </button>
    </div>
  </div>
  <div class="divider"></div>
  <div class="o-sidebar-body">
    <div class="plan-details-wrapper">
      <ng-container [ngTemplateOutlet]="selectedStudentPlan ? planDetails : productDetails"></ng-container>
    </div>
  </div>
</div>

<ng-template #planDetails>
    <div class="plan-details">
      <div class="title">Plan Details</div>
      <div class="plan-details-content">
        <div class="sub-title">Plan Name</div>
        <div class="plan-content">
            Weekly {{ selectedInstrumentName }} Music Lessons ({{ planSummaryService.getPlanType(selectedStudentPlan?.planType!) }}
            {{ planSummaryService.getPlanSummary(selectedStudentPlan?.plandetails?.items!) }})
        </div>
        <div class="space-between">
          <div>
            <div>
              <div class="sub-title">Class Type</div>
              <div class="plan-content">Recurring Plan</div>
            </div>
            <div>
              <div class="sub-title">Plan Price</div>
              <div class="plan-content mb-0">${{ selectedStudentPlan?.planPrice | number:'1.2-2' }}/Month</div>
            </div>
          </div>
          <div>
            <div>
              <div class="sub-title">Visit Per Week</div>
              <div class="plan-content">
                {{ (selectedStudentPlan?.plandetails?.items)![0].planDetail.visitsPerWeek }} visit
              </div>
            </div>
            <div>
              <div class="sub-title">Start From</div>
              <div class="plan-content mb-0">
                {{ getScheduleDate | date: "mediumDate" }} |
                {{ bookPlanFormValue.scheduleStartTime | date: "shortTime" }} -
                {{ bookPlanFormValue.scheduleEndTime | date: "shortTime" }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="dotted-divider"></div>
    <div class="plan-details">
      <div class="title">Document</div>
      <div class="plan-details-content">
        <div class="sub-title">Name</div>
        <div class="plan-content mb-0">{{ selectedStudentPlan?.documentName }}</div>
      </div>
    </div>
    <div class="dotted-divider"></div>
    <div class="plan-details">
      <div class="title">Payment Details</div>
      <div class="plan-details-content">
        <div class="space-between">
          <div class="sub-title d-flex">
            Initial Month Payment
          </div>
          <div class="plan-content" *ngIf="!showPlanPriceInput">
            ${{ currentMonthPayment | number:'1.2-2' }}
            <img [src]="constants.staticImages.icons.editPenGreen" alt="" class="ms-1 pointer" *ngIf="isPlanRenewal" (click)="showPlanPriceInput = true" matTooltip="Edit Plan Price">
          </div>
          <div class="plan-content" *ngIf="showPlanPriceInput && selectedStudentPlan">
            <mat-form-field>
              <mat-label>Plan Price</mat-label>
              <input 
                matInput 
                type="number"
                pattern="[0-9]*\.?[0-9]*"
                [(ngModel)]="selectedStudentPlan.planPrice"
                (input)="getCurrentMonthPayment()"
                [min]="0"
                placeholder="0.00">
              <span matPrefix>$&nbsp;</span>
              <button
                mat-icon-button
                matSuffix
                (click)="showPlanPriceInput = false"
                [attr.aria-label]="'Clear plan price'"
                type="button">
                <mat-icon>close</mat-icon>
              </button>
            </mat-form-field>
          </div>
        </div>
        <div class="space-between">
          <div class="sub-title d-flex">
            Service Charge
          </div>
          <div class="plan-content">
            +${{ selectedStudentPlan?.serviceFees | number:'1.2-2' }}
          </div>
        </div>
        <div class="space-between">
          <div class="sub-title d-flex">
            Registration Fees
          </div>
          <div class="plan-content">
            +${{ selectedStudentPlan?.registrationFees | number:'1.2-2' }}
          </div>
        </div>
        <div class="space-between discount-row" *ngIf="!showDiscountField">
          @if (this.constants.roleIds.CLIENT === currentUser?.userRoleId) {
            <div class="sub-title">Discount</div>
          }@else {
            <div class="discount-toggle" (click)="showDiscountField = true">
              <span class="discount-link">Apply discount</span>
            </div>
          }
          <div class="plan-content mb-0">${{ discountAmount | number:'1.2-2' }}</div>
        </div>
        
        @if (showDiscountField) {
          <div class="space-between discount-row">
            <div class="discount-input-container">
              <mat-form-field>
                <mat-label>Discount Amount</mat-label>
                <input 
                  matInput 
                  pattern="[0-9]*\.?[0-9]*"
                  [(ngModel)]="discountAmount" 
                  (input)="capDiscount($event, true); calculateTotalPrice(true)"
                  [max]="getActualPrice(true)"
                  placeholder="0.00">
                <span matPrefix>$&nbsp;</span>
                <button
                  mat-icon-button
                  matSuffix
                  (click)="clearDiscount(true)"
                  [attr.aria-label]="'Clear discount'"
                  type="button">
                  <mat-icon>close</mat-icon>
                </button>
              </mat-form-field>
              <div *ngIf="discountError" class="error-message">{{ discountError }}</div>
            </div>
            <div class="plan-content discount">-${{ discountAmount | number:'1.2-2' }}</div>
          </div>
        }
        @if(showDiscountField) {
          <mat-checkbox [(ngModel)]="isRecurringDiscount" class="mat-checkbox-custom"> Apply this discount to all future recurring payments. </mat-checkbox>
        }
        <div class="dotted-divider"></div>
        <div class="space-between">
          <div class="sub-title text-black mb-0">Total Payable Amount</div>
          <div class="plan-content mb-0">${{ totalFirstMonthPayment }}</div>
        </div>
      </div>
    </div>
    <div class="dotted-divider"></div>
    <div class="fw-bold d-flex"> <mat-icon>info</mat-icon> Note: Monthly recurring payment from next month will be <span class="primary-color ms-1">${{ isRecurringDiscount ? calculateTotalPrice(true) : selectedStudentPlan?.planPrice | number:'1.2-2' }}</span></div>
</ng-template>

<ng-template #productDetails>
  <ng-container [ngTemplateOutlet]="showPageLoader ? showLoader : productDetailsContent"></ng-container>
</ng-template>

<ng-template #productDetailsContent>
  <div class="plan-details mb-4">
    <div class="title mb-4">Product Payment Details</div>
    <div class="plan-details-content">
      @for (product of shoppingCart; track $index) {
        @if (product.status !== productStatus.OUT_OF_STOCK) {
          <div class="space-between mb-2">
            <div class="product-sub-title">{{ product.productName }} (x{{ product.quantity }})</div>
            <div class="plan-content mb-0">${{ product.productPrice * product.quantity }}</div>
          </div>
        }
      }
      
      <!-- Discount section -->
      <div class="space-between discount-row" *ngIf="!showDiscountField">
        @if (this.constants.roleIds.CLIENT === currentUser?.userRoleId) {
          <div class="sub-title">Discount</div>
        }@else {
          <div class="discount-toggle" (click)="showDiscountField = true">
            <span class="discount-link">Apply discount</span>
          </div>
        }
        <div class="plan-content mb-0">${{ discountAmount | number:'1.2-2' }}</div>
      </div>
      
      <!-- Discount input when editing -->
      <div class="space-between discount-row" *ngIf="showDiscountField">
        <div class="discount-input-container">
          <mat-form-field>
            <mat-label>Discount Amount</mat-label>
            <input 
              matInput 
              pattern="[0-9]*\.?[0-9]*"
              [(ngModel)]="discountAmount" 
              (input)="capDiscount($event, false); calculateTotalPrice(false)"
              [max]="getActualPrice(false)"
              placeholder="0.00">
            <span matPrefix>$&nbsp;</span>
            <button
              mat-icon-button
              matSuffix
              (click)="clearDiscount(false)"
              [attr.aria-label]="'Clear discount'"
              type="button">
              <mat-icon>close</mat-icon>
            </button>
          </mat-form-field>
          <div *ngIf="discountError" class="error-message">{{ discountError }}</div>
        </div>
        <div class="plan-content">${{ discountAmount | number:'1.2-2' }}</div>
      </div>
      
      <!-- To be used -->
      <!-- <div class="space-between">
        <div class="sub-title mb-3">Registration Fee</div>
        <div class="plan-content">$10</div>
      </div>
      <div class="space-between">
        <div class="sub-title">Security Deposit</div>
        <div class="plan-content mb-0">$100</div>
      </div> -->
      <div class="dotted-divider"></div>
      <div class="space-between">
        <div class="product-sub-title text-black">Total Payable Amount</div>
        <div class="plan-content mb-0">${{ calculateTotalPrice(false) | number:'1.2-2'}}</div>
      </div>
    </div>
  </div>
  <div class="title">Add Payment Details</div>
  <app-payment-methods screen="continue-to-checkout" [showDefaultPaymentBtn]="false" [accManagerDetails]="accManagerDetails" [isPaymentFailed]="isRePayment" (closeSideNav)="openAssignedPlan()"></app-payment-methods>
</ng-template>


<ng-template #showLoader>
 <div class="page-loader-wrapper">
    <app-content-loader></app-content-loader>
 </div>
</ng-template>
