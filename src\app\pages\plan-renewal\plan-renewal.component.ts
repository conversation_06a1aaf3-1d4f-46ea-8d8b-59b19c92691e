import { ChangeDetectorRef, Component, OnInit } from '@angular/core';
import { BaseComponent } from 'src/app/shared/components/base-component/base.component';
import { CommonModule } from '@angular/common';
import { DirectivesModule } from 'src/app/shared/directives/directives.module';
import { CBResponse } from 'src/app/shared/models';
import { API_URL } from 'src/app/shared/constants/api-url.constants';
import { takeUntil } from 'rxjs';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import {
  AddSchedule,
  AssignedPlanStatus,
  ClassTypes,
  StudentPlans
} from 'src/app/pages/scheduler/pages/scheduler-wrapper/pages/schedule/models';
import { SchedulerService, StudentPlanService } from 'src/app/pages/scheduler/pages/scheduler-wrapper/pages/schedule/services';
import { PlanSummaryService } from 'src/app/pages/settings/pages/plan/services';
import { SharedModule } from 'src/app/shared/shared.module';
import { Router } from '@angular/router';
import { CommonUtils } from 'src/app/shared/utils';
import { MatSidenavModule } from '@angular/material/sidenav';
import { PlanSummary } from '../settings/pages/plan/models';
import { ContinueToCheckoutComponent } from '../members/pages/students/pages/continue-to-checkout/continue-to-checkout.component';
import { DependentInformations } from '../members/pages/students/models';
import moment from 'moment';

const DEPENDENCIES = {
  MODULES: [CommonModule, DirectivesModule, MatInputModule, MatButtonModule, SharedModule, MatSidenavModule],
  COMPONENTS: [ContinueToCheckoutComponent]
};

@Component({
  selector: 'app-plan-renewal',
  standalone: true,
  imports: [...DEPENDENCIES.MODULES, ...DEPENDENCIES.COMPONENTS],
  templateUrl: './plan-renewal.component.html',
  styleUrl: './plan-renewal.component.scss'
})
export class PlanRenewalComponent extends BaseComponent implements OnInit {
  studentPlans!: Array<StudentPlans>;
  totalCount!: number;
  classType = ClassTypes;
  isPlanRenewSideNavOpen = false;
  showBtnLoaderId!: number | null;
  selectedStudentPlan!: PlanSummary | undefined;
  selectedStudentDetails!: DependentInformations | undefined;
  selectedInstrumentName!: string;
  bookPlanFormValue!: AddSchedule;
  assignedPlanStatuses = AssignedPlanStatus;
  currentDate = moment().format(this.constants.dateFormats.yyyy_MM_DD_T_HH_mm_ss);

  constructor(
    private readonly cdr: ChangeDetectorRef,
    protected readonly planSummaryService: PlanSummaryService,
    protected readonly schedulerService: SchedulerService,
    private readonly studentPlanService: StudentPlanService,
    private readonly router: Router
  ) {
    super();
  }

  ngOnInit(): void {
    this.getStudentPlansExpiringSoon();
  }

  openStudentDetailPage(dependentId: number): void {
    this.router.navigate([this.path.members.root, this.path.members.clients], {
      queryParams: { dependentId: dependentId }
    });
  }

  getRemainingDays(date: string): number {
    return CommonUtils.getRemainingDays(date);
  }

  getStudentPlansExpiringSoon(): void {
    this.showPageLoader = true;
    this.studentPlanService
      .getList<CBResponse<StudentPlans>>(API_URL.studentPlans.getAllStudentPlansExpiringSoon)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: CBResponse<StudentPlans>) => {
          this.studentPlans = res.result.items;
          this.totalCount = res.result.totalCount;
          this.showPageLoader = false;
          this.cdr.detectChanges();
        },
        error: () => {
          this.showPageLoader = false;
          this.cdr.detectChanges();
        }
      });
  }

  togglePlanRenewalSideNav(isOpen: boolean, studentPlan: StudentPlans | null): void {
    this.isPlanRenewSideNavOpen = isOpen;
    this.selectedStudentPlan = {
      ...(studentPlan as unknown as PlanSummary),
      plandetails: { items: studentPlan?.planDetails ?? [] },
      id: studentPlan?.studentplan.id ?? 0
    };
    this.selectedStudentDetails = {
      ...(studentPlan as unknown as DependentInformations),
      id: studentPlan?.studentplan.dependentInformationId ?? 0,
      firstName: studentPlan?.dependentName ?? '',
      lastName: ''
    };
    this.selectedInstrumentName = studentPlan?.instrumentName ?? '';
    this.bookPlanFormValue = {
      ...(studentPlan as unknown as AddSchedule),
      scheduleDate: studentPlan?.endDate ?? '',
      daysOfSchedule: studentPlan?.daysOfSchedule ?? []
    };
  }
}
