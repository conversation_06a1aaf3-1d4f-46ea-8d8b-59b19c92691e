import { FormArray, FormControl, FormGroup } from '@angular/forms';
import { AssignedInstructors, AssignedInstruments } from 'src/app/pages/schedule-classes/pages/ensemble-class/models';
import { IdNameModel } from 'src/app/shared/models';

export enum ClassTypes {
  INTRODUCTORY = 1,
  RECURRING = 2,
  GROUP_CLASS = 3,
  SUMMER_CAMP = 4,
  MAKE_UP = 5,
  ENSEMBLE_CLASS = 6
}

export enum LessonTypes {
  IN_PERSON = 1,
  VIRTUAL = 2
}

export interface ScheduleFilters extends AdvancedFilters {
  minScheduleDateFilter: string | null;
  maxScheduleDateFilter: string | null;
  locationIdFilter: Array<number>;
  instructorIdFilter: Array<number>;
  classTypeFilter: Array<number>;
  instrumentIdFilter: Array<number>;
  isNotShowDraftSchedule: boolean;
}

export class AdvancedFilters {
  studentIdFilter: Array<number> | Array<IdNameModel>;
  skillTypeFilter: string | null;
  roomIdFilter: Array<number>;
  lessonTypeFilter: number | null;
  studentAgeFilter: number | null;
  isSpecialNeedsFilter: boolean;
  showActiveStaffOnly: boolean;

  constructor() {
    this.studentIdFilter = [];
    this.skillTypeFilter = null;
    this.roomIdFilter = [];
    this.lessonTypeFilter = null;
    this.studentAgeFilter = null;
    this.isSpecialNeedsFilter = false;
    this.showActiveStaffOnly = true;
  }
}

export interface FilterItem {
  id: number;
  placeholder: string;
  defaultPlaceholder: string;
  value: Set<any>;
  totalCount: number;
  isOpen: boolean;
  showSearchBar: boolean;
  showClassBorder: boolean;
  options: Array<IdNameModel>;
}

export interface Filters {
  location: FilterItem;
  instructor: FilterItem;
  instrument: FilterItem;
  classType: FilterItem;
}

export interface AdvanceFiltersForParams {
  student: AdvanceFiltersParams;
}

export interface AdvanceFiltersParams {
  id: number;
  defaultPlaceholder: string;
  placeholder: string;
  value: Array<IdNameModel>;
  totalCount: number;
  showMax: number;
  isOpen: boolean;
  options: Array<IdNameModel>;
  showSearchBar: boolean;
}

export interface DependentInfoSchedule {
  dependentInformation: DependentDetails;
  userName: string;
}

export interface DependentDetails {
  firstName: string;
  lastName: string;
  age: number;
  locationId: number;
  userType: number;
  mentorId: number;
  instructorName: string;
  instrumentName: string;
  skill: string;
  id: number;
  accountManagerId?: number;
}

export interface AddScheduleFormGroup {
  id: FormControl<number | undefined>;
  classType: FormControl<number | undefined>;
  studentId: FormControl<number | undefined>;
  lessonType: FormControl<number | undefined>;
  locationId: FormControl<number | undefined>;
  instrumentId: FormControl<number | undefined>;
  instructorId: FormControl<number | undefined>;
  skillType: FormControl<string>;
  scheduleDate: FormControl<string>;
  scheduleStartTime: FormControl<string>;
  scheduleEndTime: FormControl<string>;
  planId: FormControl<number | undefined>;
  isSpecialNeedsLesson: FormControl<boolean>;
  daysOfSchedule: FormArray<FormControl<number>>;
  isAllInstances: FormControl<boolean>;
  revenueCategoryId?: FormControl<number | undefined>;
  roomId: FormControl<number | undefined>;
  isDraftSchedule?: FormControl<boolean>;
}

export interface AddSchedule {
  id: number | undefined;
  classType: number | undefined;
  studentId: number | undefined;
  lessonType: number | undefined;
  locationId: number | undefined;
  instrumentId: number | undefined;
  instructorId: number | undefined;
  skillType: string | undefined;
  scheduleDate: string | null;
  scheduleStartTime: string;
  scheduleEndTime: string;
  daysOfSchedule: number[];
  revenueCategoryId?: number | undefined;
  isDraftSchedule?: boolean;
  roomId?: number;
}

export interface UpdateScheduleFormGroup {
  id: FormControl<number | undefined>;
  classType: FormControl<number | undefined>;
  isAllInstances: FormControl<boolean | undefined>;
  scheduleStartDate: FormControl<string | null>;
  scheduleEndDate: FormControl<string | null>;
  scheduleStartTime: FormControl<string>;
  scheduleEndTime: FormControl<string>;
  isNotifyClients: FormControl<boolean>;
  notes: FormControl<string>;
  isSpecialNeedsLesson: FormControl<boolean>;
  daysOfSchedule: FormArray<FormControl<number>>;
  scheduleDays?: FormArray<FormControl<number>>;
  instructorId?: FormControl<number | undefined>;
  assignedInstructors?: FormArray<FormControl<number>>;
}

export interface CancelScheduleFormGroup {
  id: FormControl<number | undefined>;
  classType: FormControl<number | undefined>;
  isRemoveAppointment: FormControl<boolean>;
  isAllInstances: FormControl<boolean | undefined>;
  isNotifyClients: FormControl<boolean>;
  isPassGenerated: FormControl<boolean>;
  notes: FormControl<string>;
  studentId: FormControl<number | undefined>;
  instrumentId: FormControl<number | undefined>;
}

export interface EventDetails {
  classType: number;
  lessonType: number;
  studentId: number;
  studentName: string;
  locationId: number;
  locationName: string;
  instrumentId: number;
  instrumentName: string;
  instructorId: number;
  instructorName: string;
  scheduleDate: string;
  start: string;
  end: string;
  roomId: number;
  roomName: string | null;
  instrumentColor: string;
  skillType: string;
  studentAge: number;
  instrumentFontColor: string;
  isCancelSchedule: boolean;
  id: number;
}

export interface ScheduleDetail {
  scheduleLessonDetails: Array<ScheduleDetailsView>;
  leavesDetails: Array<ScheduleDetailsView>;
}

export interface ScheduleDetailsView {
  classType: number;
  lessonType: number;
  locationId: number;
  locationName: string;
  instrumentId: number;
  instrumentName: string;
  instrumentColor: string;
  instrumentFontColor: string;
  scheduleDate: string;
  start: string;
  end: string;
  roomId: number;
  roomName: string | null;
  instructorId: number;
  instructorName: string | undefined;
  instructorEmail: string;
  instructorPhoneNo: number;
  instructorProfilePhoto: string;
  isCancelSchedule: boolean;
  isDraftSchedule: boolean;
  instructorInstrument: InstructorInstrument[];
  studentDetails: StudentDetail[];
  daysOfSchedule: number[];
  id: number;
  planId: number;
  passId: number;
  notes: string;
  isSpecialNeedsLesson: boolean;
  recurringScheduleDate: Date;
  groupClassScheduleId: number;
  summerCampScheduleId: number;
  studentCapacity: number;
  groupClassName: string;
  campName: string;
  campStartDate: string;
  campEndDate: string;
  studentName: string;
  studentAge: number;
  skillType: string;
  isAllPresent: boolean;
  isLeave: boolean;
  reason: string;
  name: string;
  leaveDate: string;
  ensembleClassName:string;
  isLateCancelSchedule: boolean;
  cancelByName: string;
  cancelOn: string;
  assignedInstructors: AssignedInstructors[];
  assignedInstruments: AssignedInstruments[];
}

export interface InstructorInstrument {
  instrumentName: string;
  instrumentGrade: number;
  name: string;
  gradeLevel: number;
  id: number;
  isIntroductoryClassAvailable: boolean;
  isHeadOfDepartment: boolean;
}

export interface StudentDetail {
  studentId: number;
  studentName: string;
  studentAge: number;
  accountManagerName: string;
  accountManagerEmail: string;
  accountManagerPhoneNo: number;
  accountManagerId: number;
  grade: string;
  isPresent: boolean;
  planName: string;
}

export interface InstructorAvaibility {
  classType?: number | undefined;
  instructorId?: number | undefined;
  scheduleStartDate: string | null;
  scheduleEndDate?: string | null;
  daysOfSchedule?: number[] | string;
  isAllInstances?: boolean;
  locationId?: number | undefined;
  planId?: number | null;
  duration?: number | null;
  instrumentId?: number | undefined;
  skillType?: string;
  scheduleStartTime?: string;
  scheduleEndTime?: string;
  studentId?: number | undefined;
  instrumentIds?: number[] | number;
  instructorIds?: number[] | number;
  isIntroductoryAvaLable?: boolean;
}

export interface SuggestedTimeSlot {
  startTime: string;
  endTime: string;
  roomId: number;
}

export interface BulkCancelForm {
  instructorIds: FormControl<number[]>;
  notes: FormControl<string>;
  scheduleStartDate: FormControl<string | null>;
  scheduleEndDate: FormControl<string | null>;
  scheduleStartTime: FormControl<string>;
  scheduleEndTime: FormControl<string>;
  locationIds: FormControl<number[]>;
  isCancelAll: FormControl<boolean>;
  isPassGenerated: FormControl<boolean>;
}

export interface BulkCancelFilter {
  staff: AdvanceFiltersParams;
  location: AdvanceFiltersParams;
}
