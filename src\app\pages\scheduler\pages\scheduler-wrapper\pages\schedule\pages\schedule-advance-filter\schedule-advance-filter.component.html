<div class="o-sidebar-wrapper">
  <div class="o-sidebar-header">
    <div class="title">Calender Filter</div>
    <div class="action-btn-wrapper">
      <button
        mat-raised-button
        color="accent"
        class="mat-accent-btn back-btn"
        type="button"
        (click)="removeAllFilters()">
        Remove All
      </button>
      <button mat-raised-button color="accent" class="mat-accent-btn back-btn" type="button" (click)="closeModal()">
        Close
      </button>
      <button mat-raised-button color="primary" class="mat-primary-btn" type="button" (click)="onApplyFilters()">
        Apply
      </button>
    </div>
  </div>
  <div class="divider"></div>
  <div class="o-sidebar-body">
    <ng-container [ngTemplateOutlet]="showPageLoader ? showLoader : filterTemp"></ng-container>
  </div>
</div>
 
<ng-template #filterTemp>
  <div class="field-wrapper">
    <label for="childAge">Select Client</label>
    <div class="w-100">
      <app-multi-select-chips [filterDetail]="filterParams.student"></app-multi-select-chips>
    </div>
  </div>
  <div class="field-wrapper">
    <label for="childAge">Select Age</label>
    <div>
      <div class="btn-typed-options-wrapper">
        <div
          [ngClass]="{
            'btn-typed-option': true,
            active: !appliedAdvanceFilter.studentAgeFilter
          }"
          (click)="appliedAdvanceFilter.studentAgeFilter = null">
          All
        </div>
        @for (age of constants.ageOptions; track $index) {
          <div
            [ngClass]="{
              'btn-typed-option': true,
              active: appliedAdvanceFilter.studentAgeFilter === age.value
            }"
            (click)="appliedAdvanceFilter.studentAgeFilter = age.value">
            {{ age.label }}
          </div>
        }
      </div>
    </div>
  </div>
  <div class="field-wrapper">
    <label for="skill">Select Skill</label>
    <div>
      <div class="btn-typed-options-wrapper">
        <div
          [ngClass]="{
            'btn-typed-option': true,
            active: !appliedAdvanceFilter.skillTypeFilter
          }"
          (click)="appliedAdvanceFilter.skillTypeFilter = null">
          All
        </div>
        @for (skill of constants.skillOptions | keyvalue; track $index) {
          <div
            [ngClass]="{
              'btn-typed-option': true,
              active: appliedAdvanceFilter.skillTypeFilter === skill.value
            }"
            (click)="appliedAdvanceFilter.skillTypeFilter = skill.value">
            {{ skill.value }}
          </div>
        }
      </div>
    </div>
  </div>
  <div class="field-wrapper">
    <label>Select Lesson Type</label>
    <div class="single-btn-select-wrapper">
      <div
        [ngClass]="{ active: appliedAdvanceFilter.lessonTypeFilter === null }"
        class="select-btn"
        (click)="appliedAdvanceFilter.lessonTypeFilter = null">
        All
      </div>
      @for (lessonType of constants.lessonTypeValueOptions; track $index) {
        <div
          [ngClass]="{ active: appliedAdvanceFilter.lessonTypeFilter === lessonType.value }"
          class="select-btn"
          (click)="appliedAdvanceFilter.lessonTypeFilter = lessonType.value">
          {{ lessonType.label }}
        </div>
      }
    </div>
  </div>
  <div class="field-wrapper mb-2">
    <label>Show Active Staff Only</label>
    <mat-checkbox class="special-need" [(ngModel)]="appliedAdvanceFilter.showActiveStaffOnly">
      Show available staff only
    </mat-checkbox>
  </div>
  <div class="field-wrapper">
    <label>Special Needs Client</label>
    <mat-checkbox class="special-need" [(ngModel)]="appliedAdvanceFilter.isSpecialNeedsFilter">
      A client with special need
    </mat-checkbox>
  </div>
</ng-template>

<ng-template #showLoader>
  <div class="page-loader-wrapper">
    <app-content-loader></app-content-loader>
  </div>
</ng-template>
