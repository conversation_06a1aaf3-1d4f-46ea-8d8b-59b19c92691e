<ng-container [ngTemplateOutlet]="isLoading ? showLoader : showDayView"></ng-container>

<ng-template #showDayView>
  <div [ngClass]="{ 'day-view-wrap': true, 'day-view-wrap-one-item': schedulerDataInAccordanceOfLocation && schedulerDataInAccordanceOfLocation.length <= 1, 'no-header': currentUser$?.userRoleId === constants.roleIds.INSTRUCTOR || currentUser$?.userRoleId === constants.roleIds.SUPERVISOR }">
    @for (item of schedulerDataInAccordanceOfLocation; track $index) {
    <div [ngClass]="{ 'day-view-wrapper': true, 'mw-100': schedulerDataInAccordanceOfLocation.length <= 1}">
      <div class="day-view-header">
        <div class="empty-box"></div>
        <div class="location-name">
          <div class="name">
            {{ item?.locationName }}
          </div>
          @if (schedulerDataInAccordanceOfLocation.length > 1) {
            <mat-icon (click)="onRemoveLocation(item.locationId)">close icon</mat-icon>
          }
        </div>
      </div>
      <div class="day-view-content">
        <ng-container
          [ngTemplateOutlet]="showDayViewScheduleByLocation"
          [ngTemplateOutletContext]="{ schedulerDataByLocation: item?.schedulerData, resourcesForLocation: item?.resourcesForLocation }"></ng-container>
      </div>
    </div>
    }
  </div>
</ng-template>
 
<ng-template #showDayViewScheduleByLocation let-schedulerDataByLocation="schedulerDataByLocation" let-resourcesForLocation="resourcesForLocation">
  <mbsc-eventcalendar
    [data]="schedulerDataByLocation"
    [options]="calendarOptions"
    [scheduleEventTemplate]="eventTemplate"
    [resources]="getDisplayResources(resourcesForLocation)"
    [resourceTemplate]="myTemplate"
    (onCellDoubleClick)="onOpenAddSchedule()"
    themeVariant="light"
    theme="ios"
    [selectedDate]="showScheduleForDate">
  </mbsc-eventcalendar>
</ng-template>

<ng-template #myTemplate let-resource>
  @if (resource.isPlaceholder) {
    <div class="instructor-wrapper">
      {{ resource.name }}
    </div>
  }
  @else {
    <div class="instructor-wrapper pointer" [matTooltip]="resource.name" (click)="openInstructorDetails(resource.id)">
      {{ resource.name }}
    </div>
  }
</ng-template>

<mbsc-popup
  class="md-tooltip"
  #eventDetailsPopup
  [anchor]="detailsAnchor"
  [options]="popupOptions"
  (onClose)="schedulerDetailPopupComponent.showCancelLessonView = false">
  <app-scheduler-detail-popup
    [selectedEvent]="selectedEvent"
    (editLesson)="onEditLesson($event)"
    (closePopup)="closeEventDetailsPopup($event)"></app-scheduler-detail-popup>
</mbsc-popup>

<ng-template #eventTemplate let-data>
  @if (data.original.isLeave) {
    <div
      (click)="openEventDetailsPopup(data.original, $event)"
      [ngStyle]="{
        background: data.original.color || constants.colors.leaveBgColor
      }"
      class="schedule-item leave-event">
      <div class="schedule-border" [ngStyle]="{ 'background-color': constants.colors.leaveBorderColor }"></div>
      <div class="schedule-info-wrapper" [ngStyle]="{ color: 'white' }">
        <div class="lesson-name">{{ data.original.title || data.original.name + ' - OUT' }}</div>
        <div class="leave-reason" [matTooltip]="data.original.reason">{{ data.original.reason }}</div>
        @if (data.original.leaveType === 1) {
          <div class="leave-type">Paid Leave</div>
        } @else if (data.original.leaveType === 2) {
          <div class="leave-type">Unpaid Leave</div>
        }
      </div>
    </div>
  } @else {
    <div
      (click)="openEventDetailsPopup(data.original, $event)"
      [ngStyle]="{
        background: schedulerService.getScheduleBackgroundColor(
          data.original.start,
          data.original.isDraftSchedule,
          data.original.instrumentColor,
          data.original.classType
        )
      }"
      class="schedule-item">
    <div
      class="schedule-border"
      [ngStyle]="{
        'background-image': schedulerService.getScheduleBorderImage(
          data.original.start,
          data.original.isDraftSchedule,
          data.original.classType
        ),
        'background-color': schedulerService.getScheduleColor(
          data.original.start,
          data.original.isDraftSchedule,
          data.original.instrumentFontColor,
          data.original.classType
        )
      }"></div>
    <div
      class="schedule-info-wrapper"
      [ngStyle]="{
        color: schedulerService.getScheduleColor(
          data.original.start,
          data.original.isDraftSchedule,
          data.original.instrumentFontColor,
          data.original.classType
        )
      }">
      <div class="lesson-name" *ngIf="data.original.classType === classTypes.SUMMER_CAMP">Summer Camp</div>
      <div class="lesson-name" [ngClass]="{ strike: data.original.isCancelSchedule }">
        <span
          [ngClass]="data.original.classType === classTypes.SUMMER_CAMP ? 'camp-name' : 'lesson-name'"
          *ngIf="data.original.isCancelSchedule"
          >
          @if (data.original.isLateCancelSchedule) {
            Late Canceled:
          } @else {
            Canceled:
          }
        </span>
        @switch (data.original.classType) {
          @case (classTypes.GROUP_CLASS) {
            {{ data.original.groupClassName | titlecase }}
          }
          @case (classTypes.ENSEMBLE_CLASS) {
            {{ data.original.ensembleClassName | titlecase }}
          }
          @case (classTypes.SUMMER_CAMP) {
            {{ data.original.campName | titlecase }}
          }
          @case (classTypes.MAKE_UP) {
            {{ data.original.instrumentName }} Make-Up Lesson
          }
          @case (classTypes.INTRODUCTORY) {
            Introductory {{ data.original.instrumentName }} Lesson
          }
          @default {
            {{ data.original.instrumentName }} Lesson
          }
        }
      </div>
      <div class="instructor-info-wrapper">
        <img
          [src]="constants.staticImages.icons.multipleUserIcon" alt=""
          [ngStyle]="{
            filter: schedulerService.getScheduleColor(
              data.original.start,
              data.original.isDraftSchedule,
              data.original.instrumentFontColor,
              data.original.classType
            )
          }" />
        <div class="instructor-name" *ngIf="data.original.studentDetails?.length">
          {{ data.original.studentDetails[0].studentName | titlecase }}
          @if (data.original.studentDetails.length > 1) {
            <span [matTooltip]="getStudentNames(data.original.studentDetails)">+{{ data.original.studentDetails.length - 1 }}</span>
          }
        </div>
      </div>
    </div>
    <div class="draft-badge" *ngIf="data.original.isDraftSchedule">
      <img [src]="constants.staticImages.icons.draftBadge" height="15" alt="" />
    </div>
    </div>
  }
</ng-template>

<ng-template #showLoader>
  <div class="page-loader-wrapper">
    <app-content-loader></app-content-loader>
  </div>
</ng-template>
