import { OverlayModule } from '@angular/cdk/overlay';
import { CommonModule } from '@angular/common';
import { Component, EventEmitter, Input, OnChanges, Output, SimpleChanges, ViewChild } from '@angular/core';
import { MatIconModule } from '@angular/material/icon';
import { MbscCalendarEvent, MbscEventcalendarOptions, MbscModule, MbscPopup, MbscResource } from '@mobiscroll/angular';
import { BaseComponent } from 'src/app/shared/components/base-component/base.component';
import { SharedModule } from 'src/app/shared/shared.module';
import { SchedulerService } from '../../services';
import { ClassTypes, FilterItem, ScheduleDetailsView, StudentDetail } from '../../models';
import { CommonUtils } from 'src/app/shared/utils';
import { POPUP_OPTIONS } from 'src/app/shared/constants';
import { SchedulerDetailPopupComponent } from '../scheduler-detail-popup/scheduler-detail-popup.component';
import { IdNameModel } from 'src/app/shared/models';
import { ScheduleDataForDayView } from '../../../room-schedule/models';
import { MatTooltipModule } from '@angular/material/tooltip';
import { Account } from 'src/app/auth/models/user.model';
import { NavigationService } from 'src/app/shared/services';

const DEPENDENCIES = {
  MODULES: [MbscModule, CommonModule, SharedModule, MatIconModule, OverlayModule, MatTooltipModule],
  COMPONENTS: [SchedulerDetailPopupComponent]
};

@Component({
  selector: 'app-schedule-day-view',
  standalone: true,
  imports: [...DEPENDENCIES.MODULES, ...DEPENDENCIES.COMPONENTS],
  templateUrl: './schedule-day-view.component.html',
  styleUrl: './schedule-day-view.component.scss'
})
export class ScheduleDayViewComponent extends BaseComponent implements OnChanges {
  @Input() showScheduleForDate!: Date;
  @Input() schedulerData!: Array<MbscCalendarEvent>;
  @Input() isLoading!: boolean;
  @Input() resources!: Array<MbscResource>;
  @Input() locationFilterParam!: FilterItem;
  @Input() currentUser$!: Account | null;
  @Input() showActiveStaffOnly!: boolean;
 
  @ViewChild('eventDetailsPopup', { static: false }) eventDetailsPopup!: MbscPopup;
  @ViewChild(SchedulerDetailPopupComponent) schedulerDetailPopupComponent!: SchedulerDetailPopupComponent;

  detailsAnchor!: EventTarget | null;
  selectedEvent!: ScheduleDetailsView | undefined;
  schedulerDataInAccordanceOfLocation!: Array<ScheduleDataForDayView>;

  @Output() openScheduleUpdateModal = new EventEmitter<ScheduleDetailsView>();
  @Output() refreshScheduleData = new EventEmitter<void>();
  @Output() removeSelectedLocation = new EventEmitter<void>();
  @Output() openAddSchedule = new EventEmitter<void>();

  popupOptions = POPUP_OPTIONS;
  classTypes = ClassTypes;
  currentDate = new Date();
  yesterday = new Date(this.currentDate.getFullYear(), this.currentDate.getMonth(), this.currentDate.getDate() - 1);

  calendarOptions: MbscEventcalendarOptions = {
    view: {
      schedule: {
        type: 'day',
        startTime: '08:00',
        endTime: '22:00',
        allDay: false,
        timeCellStep: 30,
        minEventWidth: 200
      }
    },
    invalid: [
      {
        start: this.yesterday,
        end: new Date()
      }
    ]
  };

  constructor(public readonly schedulerService: SchedulerService, private readonly navigationService: NavigationService) {
    super();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['locationFilterParam']?.currentValue) {
      this.locationFilterParam = changes['locationFilterParam'].currentValue;
      this.createScheduleData();
    }
  }

  createScheduleData(): void {
    const scheduleData: Array<ScheduleDataForDayView> = [];
    const locations = this.locationFilterParam.value.size
      ? this.locationFilterParam.options.filter((location: IdNameModel) => this.locationFilterParam.value.has(location.id))
      : this.locationFilterParam.options;
    for (const location of locations) {
      const resourcesForLocation = this.getResourcesForLocation(location.id);
      scheduleData.push({
        locationId: location.id,
        locationName: location.name,
        schedulerData: this.schedulerData.filter((details: any) => details.locationId === location.id),
        resourcesForLocation: resourcesForLocation
      });
    }
    this.schedulerDataInAccordanceOfLocation = [...scheduleData];
    this.showPageLoader = false;
  }
 
  getResourcesForLocation(locationId: number): Array<MbscResource> {
    const filteredResources = this.resources?.filter((resource: any) => {
      if (resource.instructorAvailability && Array.isArray(resource.instructorAvailability)) {
        const hasAvailability = resource.instructorAvailability.some((availability: any) => availability.locationId === locationId);
        return hasAvailability;
      }
      const hasDirectLocation = resource.locationId === locationId;
      return hasDirectLocation;
    });
 
    return filteredResources;
  }
 
  getDisplayResources(resourcesForLocation?: Array<MbscResource>): Array<MbscResource> {
    let actualResources: Array<MbscResource>;
 
    if (!this.showActiveStaffOnly) {
      actualResources = this.resources;
    } else {
      actualResources = resourcesForLocation || [];
    }
 
    if (!actualResources || !actualResources.length) {
      return [{
        id: -1,
        name: 'No Instructor Available',
        isPlaceholder: true
      }];
    }
 
    return actualResources;
  }
 
  onEditLesson(scheduleDetail: ScheduleDetailsView): void {
    this.closeEventDetailsPopup(false);
    this.openScheduleUpdateModal.emit(scheduleDetail);
  }

  onOpenAddSchedule(): void {
    if (this.currentUser$?.userRoleId === this.constants.roleIds.ADMIN || this.currentUser$?.userRoleId === this.constants.roleIds.DESK_MANAGER) {
      this.openAddSchedule.emit();
    }
  }

  openInstructorDetails(instructorId: number): void {
    this.navigationService.navigateToInstructorDetail(instructorId);
  }

  openEventDetailsPopup(args: ScheduleDetailsView | undefined, event: MouseEvent): void {
    if (args?.id) {
      this.selectedEvent = args;
    }
    this.detailsAnchor = event.currentTarget || event.target;
    this.eventDetailsPopup?.open();
  }

  closeEventDetailsPopup(shouldRefreshScheduleData: boolean): void {
    if (shouldRefreshScheduleData) {
      this.refreshScheduleData.emit();
    }
    this.selectedEvent = undefined;
    this.eventDetailsPopup.close();
  }

  getTimeDiff(start: string, end: string): number {
    return CommonUtils.calculateTimeDifference(new Date(start), new Date(end));
  }

  getStudentNames(array: StudentDetail[]) {
    return array
      .slice(1)
      .map(item => item.studentName)
      .join(', ');
  }

  onRemoveLocation(id: number): void {
    this.schedulerDataInAccordanceOfLocation = this.schedulerDataInAccordanceOfLocation.filter(
      (schedule: ScheduleDataForDayView) => schedule.locationId !== id
    );
    this.locationFilterParam.value.delete(id);
    this.removeSelectedLocation.emit();
  }
}
