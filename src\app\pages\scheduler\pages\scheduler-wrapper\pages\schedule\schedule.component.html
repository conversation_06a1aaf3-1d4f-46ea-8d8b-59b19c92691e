<mat-sidenav-container class="example-container" [hasBackdrop]="true">
  @if (isAdvanceFilterOpen) {
    <mat-sidenav
      [opened]="isAdvanceFilterOpen"
      mode="over"
      position="end"
      fixedInViewport="true"
      [ngClass]="'md-sidebar'"
      [disableClose]="true">
      <app-schedule-advance-filter
        (closeFilterModal)="closeAdvanceFilterAndRestoreData()"
        (applyFilters)="applyAdvanceFilters($event)"
        [instructorIds]="getInstructorFilter()"
        [appliedAdvanceFilter]="appliedAdvanceFilter">
      </app-schedule-advance-filter>
    </mat-sidenav>
  }
  @if (isUpdateSchedule) {
    <mat-sidenav
      [opened]="isUpdateSchedule"
      mode="over"
      position="end"
      fixedInViewport="true"
      [ngClass]="'sidebar-w-750'"
      [disableClose]="true">
      <app-update-schedule
        (closeModal)="isUpdateSchedule = false"
        [selectedEvent]="selectedEvent"
        (refreshScheduleData)="setDateToCurrentDate(false)">
      </app-update-schedule>
    </mat-sidenav>
  }
  <mat-sidenav-content>
    <div class="o-card schedule-wrapper">
      <div class="o-card-body">
        <div class="calender-action-filter-wrapper">
          <div class="calender-actions">
            <div class="action-border today-btn" (click)="setDateToCurrentDate()">Today</div>
            <div class="change-date-pre-next-wrapper">
              <div class="action-border prev-next-icon-wrapper" (click)="goToPreviousDate()">
                <mat-icon>keyboard_arrow_left</mat-icon>
              </div>
              <mat-form-field class="datepicker">
                <input
                  matInput
                  [matDatepicker]="picker"
                  [ngModel]="showScheduleForDate"
                  (dateChange)="onCalendarDateChange($event.value)" />
                <mat-datepicker #picker></mat-datepicker>
              </mat-form-field>
              <div class="action-border calender-icon" (click)="picker.open()">
                <img [src]="constants.staticImages.icons.calendarIcon" alt="" />
              </div>
              <div class="action-border prev-next-icon-wrapper" (click)="goToNextDate()">
                <mat-icon>keyboard_arrow_right</mat-icon>
              </div>
            </div>
            <div class="current-date">
              @switch (selectedScheduleView) {
                @case (schedulerViews.Week) {
                  {{ firstDateOfCurrentWeek | date: constants.fullDate }} -
                  {{ lastDateOfCurrentWeek | date: constants.fullDate }}
                }
                @default {
                  {{ showScheduleForDate | date: constants.fullDate }}
                }
              }
            </div>
          </div>
          <div class="filter-wrapper">
            <div class="advance-filter-btn" (click)="openAdvanceFilterAndStoreInitialFilters()">
              <img [src]="constants.staticImages.icons.filterIcon" alt="" />
              <div>Advance Filters</div>
            </div>
            <mat-form-field class="search-bar-wrapper">
              <mat-select
                [(ngModel)]="selectedScheduleView"
                (selectionChange)="setDateToCurrentDate()">
                <mat-option
                  *ngFor="let schedulerView of schedulerViews | keyvalue: asIsOrder"
                  [value]="schedulerView.value">
                  {{ schedulerView.value }}
                </mat-option>
              </mat-select>
            </mat-form-field>
          </div>
        </div>
        <div class="filters-wrapper">
          <app-multi-select
            [filterDetail]="filterParams.location"
            (selectedFilterValues)="setDateToCurrentDate(false)"></app-multi-select>
          <app-multi-select
            *appHasPermission="[constants.roles.ADMIN, constants.roles.DESK_MANAGER, constants.roles.SUPERVISOR]"
            [filterDetail]="filterParams.instructor"
            (selectedFilterValues)="setDateToCurrentDate(false)"></app-multi-select>
          <app-multi-select
            [filterDetail]="filterParams.classType"
            (selectedFilterValues)="setDateToCurrentDate(false)"></app-multi-select>
          <app-multi-select
            [filterDetail]="filterParams.instrument"
            (selectedFilterValues)="setDateToCurrentDate(false)"></app-multi-select>
        </div>
        <div>
          @switch (selectedScheduleView) {
            @case (schedulerViews.Week) {
              <app-schedule-week-view
                [showScheduleForDate]="showScheduleForDate"
                [schedulerData]="schedulerData"
                [isLoading]="isLoading"
                [currentUser$]="currentUser"
                (openAddSchedule)="openAddSchedule.emit()"
                (refreshScheduleData)="initGetScheduleData(firstDateOfCurrentWeek, lastDateOfCurrentWeek)"
                (openScheduleUpdateModal)="openScheduleUpdateModel($event)"></app-schedule-week-view>
            }
            @case (schedulerViews.Day) {
              <app-schedule-day-view
                [showScheduleForDate]="showScheduleForDate"
                [schedulerData]="schedulerData"
                [isLoading]="isLoading"
                [currentUser$]="currentUser"
                (openScheduleUpdateModal)="openScheduleUpdateModel($event)"
                (refreshScheduleData)="initGetScheduleData(showScheduleForDate, showScheduleForDate)"
                (removeSelectedLocation)="removeSelectedLocation()"
                (openAddSchedule)="openAddSchedule.emit()"
                [resources]="resources"
                [locationFilterParam]="locationFilterParam"
                [showActiveStaffOnly]="appliedAdvanceFilter.showActiveStaffOnly"></app-schedule-day-view>
            }
            @default {
              <app-schedule-list-view
                [showScheduleForDate]="showScheduleForDate"
                [schedulerData]="schedulerData"
                [isLoading]="isLoading"
                [currentUser$]="currentUser"
                (refreshScheduleData)="initGetScheduleData(showScheduleForDate, showScheduleForDate)"
                (openScheduleUpdateModal)="openScheduleUpdateModel($event)"></app-schedule-list-view>
            }
          }
        </div>
      </div>
    </div>
  </mat-sidenav-content>
</mat-sidenav-container>
