<div class="o-sidebar-wrapper">
  <div class="o-sidebar-header">
    <div class="title">Add Group Class</div>
    <div class="action-btn-wrapper">
      <button
        mat-raised-button
        color="accent"
        class="mat-accent-btn back-btn"
        type="button"
        (click)="closeSideNavFun()">
        Close
      </button>
      <button
        mat-raised-button
        color="primary"
        class="mat-primary-btn"
        type="button"
        (click)="checkCapacity()"
        [appLoader]="showBtnLoader">
        Save
      </button>
    </div>
  </div>
  <div class="divider"></div>
  <div class="o-sidebar-body">
    <form [formGroup]="groupClassFormGroup">
      <div class="group-class-form-wrapper">
        <div class="field-wrapper">
          <label class="required mb-0">Select Age</label>
          <div>
            <div class="btn-typed-options-wrapper">
              @for (age of constants.ageOptions; track $index) {
                <div
                  [ngClass]="{
                    'btn-typed-option': true,
                    active: groupClassFormGroup.controls.ageGroup.value === age.value
                  }"
                  (click)="setFormControlValue('ageGroup', age.value)">
                  {{ age.label }}
                </div>
              }
            </div>
            <mat-error
              *ngIf="
                !groupClassFormGroup.controls.ageGroup.value &&
                (groupClassFormGroup.controls.ageGroup.touched || groupClassFormGroup.controls.ageGroup.dirty)
              "
              class="mat-error-position">
              <app-error-messages [control]="groupClassFormGroup.controls.ageGroup"></app-error-messages>
            </mat-error>
          </div>
        </div>

        <div class="field-wrapper">
          <label class="required mb-0">Select Instrument</label>
          <div>
            <div class="btn-typed-options-wrapper">
              @for (instrument of instruments; track $index) {
                <div
                  [ngClass]="{
                    'btn-typed-option btn-typed-option-wrap': true,
                    active: instrument.instrumentDetail.id === groupClassFormGroup.controls.instrumentId.value
                  }"
                  (click)="
                    setFormControlValue('instrumentId', instrument.instrumentDetail.id);
                    setFormControlValue('instrumentName', instrument.instrumentDetail.name)
                  ">
                  {{ instrument.instrumentDetail.name }}
                </div>
              }
            </div>
            <mat-error
              *ngIf="
                !groupClassFormGroup.controls.instrumentId.value &&
                (groupClassFormGroup.controls.instrumentId.touched || groupClassFormGroup.controls.instrumentId.dirty)
              "
              class="mat-error-position">
              <app-error-messages [control]="groupClassFormGroup.controls.instrumentId"></app-error-messages>
            </mat-error>
          </div>
        </div>

        <div class="field-wrapper">
          <label class="required mb-0">Select Durations</label>
          <div>
            <div class="btn-typed-options-wrapper w-100">
              @for (duration of durations | enumToKeyValue; track $index) {
                <div
                  [ngClass]="{
                    'btn-typed-option': true,
                    active: groupClassFormGroup.controls.duration.value === duration.value
                  }"
                  (click)="setFormControlValue('duration', duration.value)">
                  {{ duration.value }} Min
                </div>
              }
            </div>
            <mat-error
              *ngIf="
                !groupClassFormGroup.controls.duration.value &&
                (groupClassFormGroup.controls.duration.touched || groupClassFormGroup.controls.duration.dirty)
              ">
              <app-error-messages [control]="groupClassFormGroup.controls.duration"></app-error-messages>
            </mat-error>
          </div>
        </div>

        <div class="field-wrapper">
          <label class="required mb-0">Select Skill</label>
          <div>
            <div class="btn-typed-options-wrapper">
              @for (skill of constants.skillOptions | keyvalue: asIsOrder; track $index) {
                <div
                  [ngClass]="{
                    'btn-typed-option': true,
                    active: groupClassFormGroup.controls.skillType.value === skill.value
                  }"
                  (click)="setFormControlValue('skillType', skill.value)">
                  {{ skill.value }}
                </div>
              }
            </div>
            <mat-error
              *ngIf="
                !groupClassFormGroup.controls.skillType.value &&
                (groupClassFormGroup.controls.skillType.touched || groupClassFormGroup.controls.skillType.dirty)
              "
              class="mat-error-position">
              <app-error-messages [control]="groupClassFormGroup.controls.skillType"></app-error-messages>
            </mat-error>
          </div>
        </div>

        <div class="field-wrapper">
          <label class="required mb-0">Select Lesson Type</label>
          <div>
            <div class="single-btn-select-wrapper">
              @for (lessonType of constants.lessonTypeValueOptions; track $index) {
                <div
                  [ngClass]="{ active: groupClassFormGroup.controls.lessonType.value === lessonType.value }"
                  class="select-btn"
                  (click)="setFormControlValue('lessonType', lessonType.value)">
                  {{ lessonType.label }}
                </div>
              }
            </div>
            <mat-error
              *ngIf="
                !groupClassFormGroup.controls.lessonType.value &&
                (groupClassFormGroup.controls.lessonType.touched || groupClassFormGroup.controls.lessonType.dirty)
              "
              class="mat-error-position">
              <app-error-messages [control]="groupClassFormGroup.controls.lessonType"></app-error-messages>
            </mat-error>
          </div>
        </div>

        <div class="field-wrapper field-with-mat-inputs">
          <label>Description</label>
          <mat-form-field>
            <mat-label>Enter description here</mat-label>
            <textarea
              matInput
              formControlName="description"
              cdkTextareaAutosize
              cdkAutosizeMinRows="3"
              cdkAutosizeMaxRows="10"></textarea>
            <mat-error>
              <app-error-messages [control]="groupClassFormGroup.controls.description"></app-error-messages>
            </mat-error>
          </mat-form-field>
        </div>

        <div class="field-wrapper field-with-mat-inputs">
          <label class="required">Select Location</label>
          <div class="w-100">
            <mat-form-field class="w-100 mat-select-custom">
              <mat-select
                formControlName="locationId"
                placeholder="Select Location"
                (selectionChange)="getRoomsAndInstructors()">
                <mat-option *ngFor="let location of locations" [value]="location.schoolLocations.id">
                  {{ location.schoolLocations.locationName }}
                </mat-option>
              </mat-select>
              <mat-error>
                <app-error-messages [control]="groupClassFormGroup.controls.locationId"></app-error-messages>
              </mat-error>
            </mat-form-field>
          </div>
        </div>

        @if (groupClassFormGroup.controls.locationId.value) {
          <div class="field-wrapper field-with-mat-inputs">
            <label class="required">Select Room</label>
            <div class="w-100">
              <mat-form-field class="w-100 mat-select-custom">
                <mat-select formControlName="roomId" placeholder="Select Room">
                  @if (rooms && rooms.length) {
                    <mat-option *ngFor="let room of rooms" [value]="room.roomDetail.id">
                      {{ room.roomDetail.roomName }}
                    </mat-option>
                  } @else {
                    <mat-option>No Room Available</mat-option>
                  }
                </mat-select>
                <mat-error>
                  <app-error-messages [control]="groupClassFormGroup.controls.roomId"></app-error-messages>
                </mat-error>
              </mat-form-field>
            </div>
          </div>
        }

        <div class="field-content">
          <div class="field-wrapper field-with-mat-inputs">
            <label class="required">Client Capacity</label>
            <mat-form-field class="mat-select-custom">
              <input matInput type="number" placeholder="Client Capacity" formControlName="studentCapacity" />
              <mat-error>
                <app-error-messages [control]="groupClassFormGroup.controls.studentCapacity"></app-error-messages>
              </mat-error>
            </mat-form-field>
          </div>
          <!-- <div class="field-wrapper field-with-mat-inputs ms-4">
            <mat-checkbox class="mat-checkbox-custom mb-4" formControlName="isWaitlistAvailable"> Waitlist Available </mat-checkbox>
          </div> -->
        </div>

        <div class="field-wrapper field-with-mat-inputs">
          <label class="required">Start Date - End Date</label>
          <div class="field-content">
            <mat-form-field class="mat-start-date w-100">
              <input
                matInput
                [matDatepicker]="startPicker"
                (click)="startPicker.open()"
                formControlName="scheduleStartDate"
                (dateInput)="setEnrollDate()"
                (dateChange)="getSuggestedTimeAndInstructors(true)"
                placeholder="Select Start Date"
                [min]="maxDate" />
              <mat-datepicker-toggle matSuffix [for]="startPicker"></mat-datepicker-toggle>
              <mat-datepicker #startPicker></mat-datepicker>
              <mat-error>
                <app-error-messages [control]="groupClassFormGroup.controls.scheduleStartDate"></app-error-messages>
              </mat-error>
            </mat-form-field>
            <div class="dash mb-4">-</div>
            <mat-form-field class="mat-start-date w-100">
              <input
                matInput
                [matDatepicker]="endPicker"
                (click)="endPicker.open()"
                formControlName="scheduleEndDate"
                (dateChange)="getSuggestedTimeAndInstructors(false)"
                placeholder="Select End Date"
                [min]="
                  groupClassFormGroup.controls.scheduleStartDate.value
                    ? groupClassFormGroup.controls.scheduleStartDate.value
                    : maxDate
                " />
              <mat-datepicker-toggle matSuffix [for]="endPicker"></mat-datepicker-toggle>
              <mat-datepicker #endPicker></mat-datepicker>
              <mat-error>
                <app-error-messages [control]="groupClassFormGroup.controls.scheduleEndDate"></app-error-messages>
              </mat-error>
            </mat-form-field>
          </div>
        </div>

        <div class="field-wrapper field-with-mat-inputs">
          <label class="required">Enroll Last Date</label>
          <mat-form-field class="mat-start-date w-100">
            <input
              matInput
              [matDatepicker]="enrollPicker"
              (click)="enrollPicker.open()"
              formControlName="enrollLastDate"
              placeholder="Select Enroll Last Date"
              [min]="maxDate" />
            <mat-datepicker-toggle matSuffix [for]="enrollPicker"></mat-datepicker-toggle>
            <mat-datepicker #enrollPicker></mat-datepicker>
            <mat-error>
              <app-error-messages [control]="groupClassFormGroup.controls.enrollLastDate"></app-error-messages>
            </mat-error>
          </mat-form-field>
        </div>

        <div class="field-wrapper">
          <label class="required mb-0">Select days of the week</label>
          <div>
            <div class="single-btn-select-wrapper">
              @for (day of constants.daysOfTheWeek; track $index) {
                <div
                  class="select-btn"
                  [ngClass]="{
                    active: groupClassFormGroup.controls.daysOfSchedule.value.toString() === day.value.toString()
                  }"
                  (click)="setFormControlValue('daysOfSchedule', day.value)">
                  {{ day.label }}
                </div>
              }
            </div>
            <mat-error
              *ngIf="
                !groupClassFormGroup.controls.daysOfSchedule.value &&
                (groupClassFormGroup.controls.daysOfSchedule.touched ||
                  groupClassFormGroup.controls.daysOfSchedule.dirty)
              "
              class="mat-error-position">
              <app-error-messages [control]="groupClassFormGroup.controls.daysOfSchedule"></app-error-messages>
            </mat-error>
          </div>
        </div>

        @if (
          getInstructorAvailability.scheduleStartDate &&
          getInstructorAvailability.scheduleEndDate &&
          getInstructorAvailability.locationId &&
          getInstructorAvailability.daysOfSchedule &&
          getInstructorAvailability.duration
        ) {
          <div class="field-wrapper field-with-mat-inputs">
            <label class="required">Select Instructor</label>
            <div class="w-100">
              <mat-form-field class="w-100 mat-select-custom">
                <mat-select
                  formControlName="instructorId"
                  placeholder="Select Instructor"
                  (selectionChange)="getSuggestedTime()">
                  @if (instructors?.length) {
                    <mat-option
                      *ngFor="let instructor of instructors"
                      [value]="instructor?.id"
                      [disabled]="!instructor.isAvailable">
                      <div class="d-flex justify-content-between">
                        <div class="instructor-name">{{ instructor?.name }}</div>
                        <div *ngIf="!instructor.isAvailable" class="text-red">Busy</div>
                      </div>
                    </mat-option>
                  } @else {
                    <mat-option>No Instructor Available</mat-option>
                  }
                </mat-select>
                <mat-error>
                  <app-error-messages [control]="groupClassFormGroup.controls.instructorId"></app-error-messages>
                </mat-error>
              </mat-form-field>
            </div>
          </div>
        }

        <div class="field-wrapper">
          <label class="required mb-0">Select Time Slot</label>
          <div class="w-100">
            <mat-form-field class="w-100 mat-select-custom time">
              <mat-select placeholder="Select Time" [(value)]="selectedTimeSlot">
                @if (groupClassFormGroup.getRawValue().instructorId && suggestedTimeSlots?.length) {
                  <mat-option
                    *ngFor="let suggestedTimeSlot of suggestedTimeSlots"
                    (click)="setStartAndEndTime(suggestedTimeSlot)"
                    [value]="suggestedTimeSlot.startTime + ' - ' + suggestedTimeSlot.endTime">
                    {{ suggestedTimeSlot.startTime | date: "shortTime" }} -
                    {{ suggestedTimeSlot.endTime | date: "shortTime" }}
                  </mat-option>
                } @else if (!suggestedTimeSlots?.length && !groupClassFormGroup.controls.instructorId.value) {
                  <mat-option>Please select an Instructor to see time slots</mat-option>
                }
                @else {
                  <mat-option>No Time Slot Available</mat-option>
                }
              </mat-select>
            </mat-form-field>
            <mat-error
              *ngIf="
                !groupClassFormGroup.controls.scheduleStartTime.value &&
                (groupClassFormGroup.controls.scheduleStartTime.touched ||
                  groupClassFormGroup.controls.scheduleStartTime.dirty)
              "
              class="mat-error-position">
              <app-error-messages [control]="groupClassFormGroup.controls.scheduleStartTime"></app-error-messages>
            </mat-error>
          </div>
        </div>

        <div class="field-wrapper field-with-mat-inputs">
          <label class="required">Price</label>
          <mat-form-field class="mat-select-custom">
            <input matInput type="number" placeholder="Price" formControlName="price" />
            <span matTextPrefix>$&nbsp;</span>
            <mat-error>
              <app-error-messages [control]="groupClassFormGroup.controls.price"></app-error-messages>
            </mat-error>
          </mat-form-field>
        </div>

        <div class="field-wrapper field-with-mat-inputs">
          <label class="required">Select Revenue Category</label>
          <div class="w-100">
            <mat-form-field class="w-100 mat-select-custom">
              <mat-select formControlName="categoryId" placeholder="Select Revenue Category">
                <mat-option *ngFor="let revenueCategory of revenueCategories" [value]="revenueCategory.category.id">
                  {{ revenueCategory.category.categoryName }}
                </mat-option>
              </mat-select>
              <mat-error>
                <app-error-messages [control]="groupClassFormGroup.controls.categoryId"></app-error-messages>
              </mat-error>
            </mat-form-field>
          </div>
        </div>

        <div class="field-wrapper field-with-mat-inputs">
          <label>Group Class Name</label>
          <mat-form-field class="mat-select-custom">
            <input matInput placeholder="Group Class Name" formControlName="groupClassName" />
            <mat-error>
              <app-error-messages [control]="groupClassFormGroup.controls.groupClassName"></app-error-messages>
            </mat-error>
          </mat-form-field>
        </div>
      </div>
    </form>
  </div>
</div>
