import { ChangeDetectorRef, Component, EventEmitter, Input, OnChanges, Output, SimpleChanges } from '@angular/core';
import { SharedModule } from 'src/app/shared/shared.module';
import { BaseComponent } from 'src/app/shared/components/base-component/base.component';
import { CommonModule } from '@angular/common';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { takeUntil } from 'rxjs';
import { CBGetResponse } from 'src/app/shared/models';
import { API_URL } from 'src/app/shared/constants/api-url.constants';
import { StoreProductService } from '../../services';
import { CustomerOrdersRes, ProductStatus, StoreProductDetails } from '../../models';
import { SafePipe } from 'src/app/shared/pipe';

const DEPENDENCIES = {
  MODULES: [CommonModule, MatButtonModule, SharedModule, MatIconModule, MatIconModule, SafePipe]
};

@Component({
  selector: 'app-view-product',
  standalone: true,
  imports: [...DEPENDENCIES.MODULES],
  templateUrl: './view-product.component.html',
  styleUrl: './view-product.component.scss'
})
export class ViewProductComponent extends BaseComponent implements OnChanges {
  @Input() selectedProductId!: number | undefined;
  @Input() selectedProduct!: CustomerOrdersRes | null;
  @Input() isFromMembers!: boolean;

  productDetail!: StoreProductDetails;
  productStatus = ProductStatus;

  @Output() closeSideNav = new EventEmitter<void>();
  @Output() openEditSideNav = new EventEmitter<number>();
  @Output() deleteProduct = new EventEmitter<number>();
  @Output() addToCart = new EventEmitter<void>();

  constructor(
    private readonly cdr: ChangeDetectorRef,
    private readonly storeProductService: StoreProductService
  ) {
    super();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['selectedProductId']?.currentValue) {
      this.selectedProductId = changes['selectedProductId'].currentValue;
      this.getProductDetailById(this.selectedProductId!);
    }
  }

  getProductDetailById(id: number | undefined): void {
    this.showPageLoader = true;
    this.storeProductService
      .getListWithFilters<CBGetResponse<StoreProductDetails>>({id: id}, `${API_URL.storeProduct.getStoreProductForView}`)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: CBGetResponse<StoreProductDetails>) => {
          this.productDetail = res.result;
          this.showPageLoader = false;
          this.cdr.detectChanges();
        },
        error: () => {
          this.showPageLoader = false;
          this.cdr.detectChanges();
        }
      });
  }

  isImageOrPDF(image?: string): string {
    if (image?.includes('.pdf')) {
      return 'pdf';
    } else {
      return 'image';
    }
  }

  onEdit(): void {
    this.openEditSideNav.emit(this.productDetail.id);
  }

  onDelete(): void {
    this.deleteProduct.emit(this.productDetail.id);
  }

  closeViewSideNavFun(): void {
    this.closeSideNav.emit();
  }

  addToCartFun(): void {
    this.addToCart.emit();
  }
}
