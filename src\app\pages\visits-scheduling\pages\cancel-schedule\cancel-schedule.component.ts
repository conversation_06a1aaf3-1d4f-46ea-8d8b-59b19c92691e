import { CommonModule } from '@angular/common';
import { AfterViewInit, ChangeDetectorRef, Component, EventEmitter, Input, OnChanges, OnInit, Output, SimpleChanges } from '@angular/core';
import { FormControl, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSidenavModule } from '@angular/material/sidenav';
import { takeUntil } from 'rxjs';
import { CancelScheduleFormGroup, ClassTypes } from 'src/app/pages/scheduler/pages/scheduler-wrapper/pages/schedule/models';
import { SchedulerService } from 'src/app/pages/scheduler/pages/scheduler-wrapper/pages/schedule/services';
import { BaseComponent } from 'src/app/shared/components/base-component/base.component';
import { API_URL } from 'src/app/shared/constants/api-url.constants';
import { AppToasterService } from 'src/app/shared/services';
import { SharedModule } from 'src/app/shared/shared.module';
import { CurrentUserScheduleLessonDetail } from '../../models';
import moment from 'moment';
import { Router } from '@angular/router';
import { CommonUtils } from 'src/app/shared/utils';
import { AuthService } from 'src/app/auth/services';
import { MatSelectModule } from '@angular/material/select';
import { CBResponse } from 'src/app/shared/models';
import { StudentGrades } from 'src/app/pages/members/pages/students/models';
import { StudentGradeService } from 'src/app/pages/members/pages/students/services';
import { DependentInfo, SignUpForOptions } from 'src/app/auth/models';

const DEPENDENCIES = {
  MODULES: [
    SharedModule,
    MatSidenavModule,
    MatButtonModule,
    CommonModule,
    MatFormFieldModule,
    MatInputModule,
    ReactiveFormsModule,
    MatSelectModule
  ]
};

@Component({
  selector: 'app-cancel-schedule',
  standalone: true,
  imports: [...DEPENDENCIES.MODULES],
  templateUrl: './cancel-schedule.component.html',
  styleUrl: './cancel-schedule.component.scss'
})
export class CancelScheduleComponent extends BaseComponent implements OnInit, OnChanges, AfterViewInit {
  @Input() selectedScheduleDetails!: CurrentUserScheduleLessonDetail | null;

  cancelScheduleForm!: FormGroup<CancelScheduleFormGroup>;
  studentInstruments!: Array<StudentGrades>;
  studentList!: Array<DependentInfo>;
  isPassGenerated = false;
  classTypes = ClassTypes;

  @Output() refreshScheduleData = new EventEmitter<void>();
  @Output() closeSideNav = new EventEmitter<void>();

  constructor(
    private readonly toasterService: AppToasterService,
    private readonly schedulerService: SchedulerService,
    private readonly authService: AuthService,
    private readonly studentGradeService: StudentGradeService,
    private readonly cdr: ChangeDetectorRef,
    private readonly router: Router
  ) {
    super();
  }

  ngOnInit(): void {
    this.initCancelScheduleForm();
    this.getCurrentUser();
  }

  ngAfterViewInit(): void {
    this.cancelScheduleForm?.patchValue({ ...this.selectedScheduleDetails });
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['selectedScheduleDetails']?.currentValue) {
      this.selectedScheduleDetails = changes['selectedScheduleDetails']?.currentValue;
      this.getStudentGrades(this.selectedScheduleDetails?.studentId!);
    }
  }

  initCancelScheduleForm(): void {
    this.cancelScheduleForm = new FormGroup<CancelScheduleFormGroup>({
      classType: new FormControl(undefined, { nonNullable: true, validators: [Validators.required] }),
      id: new FormControl(undefined, { nonNullable: true, validators: [Validators.required] }),
      isRemoveAppointment: new FormControl(false, { nonNullable: true, validators: [Validators.required] }),
      isAllInstances: new FormControl(false, { nonNullable: true, validators: [Validators.required] }),
      isNotifyClients: new FormControl(true, { nonNullable: true, validators: [Validators.required] }),
      isPassGenerated: new FormControl(true, { nonNullable: true, validators: [Validators.required] }),
      notes: new FormControl('', { nonNullable: true, validators: [Validators.required] }),
      studentId: new FormControl(undefined, { nonNullable: true, validators: [Validators.required] }),
      instrumentId: new FormControl(undefined, { nonNullable: true, validators: [Validators.required] })
    });
  }

  isWithinNext24Hours(start?: string): boolean {
    const formattedStartDate = moment(new Date(start!));
    const diffInMinutes = formattedStartDate?.diff(new Date(), 'minutes');

    return diffInMinutes > 0 && diffInMinutes <= 1440;
  }

  onCancelSchedule(): void {
    const isWithinNext24Hours = this.isWithinNext24Hours(this.selectedScheduleDetails?.start);

    if (this.cancelScheduleForm.invalid) {
      this.cancelScheduleForm.markAllAsTouched();
      return;
    }
    this.cancelScheduleForm.markAsUntouched();
    this.showBtnLoader = true;
    this.schedulerService
      .add(
        {
          ...this.cancelScheduleForm.getRawValue(),
          isPassGenerated: !isWithinNext24Hours,
          currentDateTime: new Date().toUTCString()
        },
        API_URL.scheduleLessonDetails.cancelLesson
      )
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: () => {
          this.showBtnLoader = false;
          this.handleSuccessfulCancellation(isWithinNext24Hours);
          this.cdr.detectChanges();
        },
        error: () => {
          this.showBtnLoader = false;
          this.cdr.detectChanges();
        }
      });
  }

  handleSuccessfulCancellation(isWithinNext24Hours: boolean): void {
    if (!isWithinNext24Hours && this.selectedScheduleDetails?.classType === ClassTypes.RECURRING) {
      this.isPassGenerated = true;
    } else {
      this.isPassGenerated = false;
      this.onCloseSideNav();
      this.refreshScheduleData.emit();
      this.toasterService.success(this.constants.successMessages.canceledSuccessfully.replace('{item}', 'Lesson'));
    }
  }

  getCurrentUser(): void {
    this.authService
      .getCurrentUser()
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: res => {
          this.currentUser = res;
          this.getStudentList();
          this.cdr.detectChanges();
        },
        error: () => {
          this.cdr.detectChanges();
        }
      });
  }

  getStudentGrades(studentId: number): void {
    this.studentGradeService
      .getList<CBResponse<StudentGrades>>(`${API_URL.studentGrades.getAllByStudentId}?studentId=${studentId}`)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: CBResponse<StudentGrades>) => {
          this.studentInstruments = res.result.items;
          this.cdr.detectChanges();
        },
        error: () => {
          this.cdr.detectChanges();
        }
      });
  }

  getStudentList(): void {
    this.studentList = [];
    if (this.currentUser?.userType === SignUpForOptions.YOURSELF_AND_CHILD) {
      this.studentList = [...(this.currentUser?.dependentDetails || [])];
      const isCurrentUserInList = this.studentList.some(student => student.id === this.currentUser?.dependentId);

      if (!isCurrentUserInList) {
        this.studentList.push({
          ...this.currentUser,
          id: this.currentUser.dependentId,
          firstName: this.currentUser.firstName,
          lastName: this.currentUser.lastName
        });
      }
    } else if (this.currentUser?.userType === SignUpForOptions.YOUR_CHILD) {
      this.studentList = this.currentUser?.dependentDetails;
    }
  }

  resetInstrumentSelection(): void {
    this.cancelScheduleForm.get('instrumentId')?.setValue(undefined);
  }

  getTimeDiff(start: string, end: string): number {
    return CommonUtils.calculateTimeDifference(new Date(start), new Date(end));
  }

  onScheduleLesson(): void {
    const duration = this.getTimeDiff(this.selectedScheduleDetails?.start!, this.selectedScheduleDetails?.end!);
    this.router.navigate([this.path.visits.root, this.path.scheduleMakeUpLesson], {
      queryParams: { scheduleId: this.selectedScheduleDetails?.id, d: duration }
    });
  }

  onCloseSideNav(): void {
    this.closeSideNav.emit();
    this.cancelScheduleForm.reset();
    if (this.isPassGenerated) {
      this.refreshScheduleData.emit();
      this.isPassGenerated = false;
    }
  }
}
