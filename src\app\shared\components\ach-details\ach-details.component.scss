@import 'src/assets/scss/theme/_mixins.scss';
@import 'src/assets/scss/variables';

.auth-page-wrapper {
  height: unset !important;
  
  .card-container {
    .card-details {
      margin-bottom: 20px;
    }

    .o-card {
      background-color: $gray-bg-light;
      border-radius: 8px;

      .o-card-body {
        padding: 0;
      }
    }
  }
}

.account-content {
  display: flex;
  flex-direction: column;

  .account-section {
    padding: 16px 20px;

    &:first-child {
      border-bottom: 1px solid $gray-bg-light;
    }

    .section-header {
      display: flex;
      align-items: center;
      margin-bottom: 16px;

      .section-icon {
        color: $primary-color;
        margin-right: 10px;
      }

      .section-title {
        font-size: 16px;
        font-weight: 600;
        color: $black-color;
        margin: 0;
      }
    }

    .section-body {
      .detail-row {
        display: flex;
        margin-bottom: 8px;

        &:last-child {
          margin-bottom: 0;
        }

        .detail-label {
          width: 140px;
          font-size: 14px;
          color: $gray-text;
          font-weight: 500;
        }

        .detail-value {
          font-size: 14px;
          color: $black-color;

          &.name-value {
            font-weight: 600;
          }
        }
      }
    }
  }
}

.card-info {
  position: absolute;
  top: 16px;
  right: 16px;

  .card-buttons {
    img {
      width: 20px;
      height: 20px;
      cursor: pointer;
    }
  }
}

.o-card {
  position: relative;

  &:hover {
    .card-info {
      opacity: 1;
    }
  }
}

.action-btn-wrapper {
  margin-top: 20px;

  .tab-btn-content {
    display: flex;
    justify-content: flex-end;

    button {
      min-width: 120px;
    }
  }
}

@media (min-width: 768px) {
  .account-content {
    flex-direction: row;

    .account-section {
      flex: 1;

      &:first-child {
        border-bottom: none;
        border-right: 1px solid $gray-bg-light;
      }
    }
  }
}

@media (max-width: 767px) {
  .auth-page-wrapper {
    padding: 15px;

    .card-container {
      .o-card {
        .o-card-body {
          padding: 0;
        }
      }
    }
  }

  .account-content {
    .account-section {
      padding: 12px 15px;

      .section-header {
        margin-bottom: 12px;

        .section-title {
          font-size: 15px;
        }
      }

      .section-body {
        .detail-row {
          margin-bottom: 6px;

          .detail-label,
          .detail-value {
            font-size: 13px;
          }
        }
      }
    }
  }

  .action-btn-wrapper {
    .tab-btn-content {
      justify-content: center;

      button {
        width: 100%;
      }
    }
  }
}
