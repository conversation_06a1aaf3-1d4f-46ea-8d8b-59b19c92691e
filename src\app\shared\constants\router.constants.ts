export const ROUTER_PATHS = {
  root: '/',
  auth: {
    root: 'auth',
    login: 'login',
    forgotPassword: {
      root: 'forgot-password',
      init: 'init',
      finish: 'finish'
    },
    signUp: 'sign-up'
  },
  requestInformation: 'request-information',
  scheduleIntroductoryLesson: 'schedule-introductory-lesson',
  dashboard: {
    root: 'dashboard'
  },
  referral: {
    root: 'referral'
  },
  users: {
    root: 'users',
    list: 'list'
  },
  profile: {
    root: 'profile',
    updateCurrentUserProfile: 'UpdateCurrentUserProfile'
  },
  error: {
    notFound: 'not-found'
  },
  help: {
    root: 'help'
  },
  document: {
    root: 'document'
  },
  billing:{
    root: 'billing',
    product: 'product-bill',
    planAndPass: 'plan-and-pass-bill'
  },
  roomAndLocation: {
    root: 'room-and-location'
  },
  settings: {
    root: 'settings',
    help: 'help',
    quickLink: 'quick-link',
    referral: 'referral',
    rewardSetting: 'reward-setting',
    document: 'document',
    plan: 'plan',
    groupClass: 'group-class',
    ensembleClass:'ensemble-class',
    passes: 'passes',
    summerCamp: 'summer-camp',
    revenueCategory: 'revenue-category'
  },
  members: {
    root: 'members',
    instructors: 'instructors',
    clients: 'clients',
    supervisors: 'supervisors',
    deskManagers: 'desk-managers'
  },
  schedule: {
    root: 'schedule'
  },
  visits: {
    root: 'visits'
  },
  crud: {
    create: 'create',
    view: 'view',
    edit: 'edit',
    id: ':id'
  },
  scheduleClasses: {
    root: 'schedule-classes',
    groupClasses: 'group-classes',
    ensembleClasses: 'ensemble-classes',
    introductoryLesson: 'introductory-lesson',
    summerCamp: 'summer-camp'
  },
  plansAndPasses: {
    root: 'plans-and-passes'
  },
  scheduleMakeUpLesson: 'schedule-make-up-lesson',
  attendance: 'attendance',
  clientInquiry: 'client-inquiry',
  request: {
    root: 'request',
    planCancelRequest: 'plan-cancel-request',
    leaveRequest: 'leave-request',
    maintenanceRequest: 'maintenance-request',
  },
  message: 'message',
  shop: 'shop',
  pendingPayments: 'pending-payments',
  planRenewal: 'plan-renewal'
};
